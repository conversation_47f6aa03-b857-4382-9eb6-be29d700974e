/* -*- C++ -*- */

/****************************************************************************
** Copyright (c) 2001-2014
**
** This file is part of the QuickFIX FIX Engine
**
** This file may be distributed under the terms of the quickfixengine.org
** license as defined by quickfixengine.org and appearing in the file
** LICENSE included in the packaging of this file.
**
** This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
** WARRANTY OF DESIGN, ME<PERSON><PERSON><PERSON>ABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
**
** See http://www.quickfixengine.org/LICENSE for licensing information.
**
** Contact <EMAIL> if any conditions of this licensing are
** not clear to you.
**
****************************************************************************/

#ifndef ORDERMATCH_IDGENERATOR_H
#define ORDERMATCH_IDGENERATOR_H

#include <string>
#include <sstream>

class IDGenerator
{
public:
IDGenerator() : m_orderID( 0 ), m_executionID( 0 ) {}

  std::string genOrderID()
  {
    return std::to_string(++m_orderID);
  }

  std::string genExecutionID()
  {
    return std::to_string(++m_executionID);
  }

private:
  long m_orderID;
  long m_executionID;
};

#endif
