<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>
  <link href="doxygen.css" rel="stylesheet" type="text/css">

  <title>QuickFIX Dependencies</title>
</head>

<body>
  <div class='header'>
    <div class='headertitle'>
      QuickFIX Dependencies
    </div>
  </div>

  <div class='contents'>
    <h1>Required Dependencies</h1>

    <table border="1" cellspacing="0" cellpadding="5" width="100%">
      <tbody>
        <tr bgcolor="#DDDDDD">
          <th></th>

          <th>Run</th>

          <th>Build</th>

          <th>Test</th>
        </tr>

        <tr bgcolor="#FFFFFF">
          <td align="center"><b>Windows</b></td>

          <td></td>

          <td>
            <a href="http://msdn.microsoft.com/visualc/">Microsoft
            Visual C++</a><br>
          </td>

          <td>
            <a>Ruby</a><br>
          </td>
        </tr>

        <tr>
          <td align="center"><b>Linux</b></td>

          <td>
            <a href=
            "http://www.rpmfind.net/linux/rpm2html/search.php?query=glibc">
            glibc</a><br>
          </td>

          <td>
            <a href=
            "http://www.rpmfind.net/linux/rpm2html/search.php?query=gcc">
            gcc</a><br>
            <a href=
            "http://www.rpmfind.net/linux/rpm2html/search.php?query=gcc-c%2B%2B">
            gcc-c++</a><br>
            <a href=
            "http://www.rpmfind.net/linux/rpm2html/search.php?query=glibc-devel">
            glibc-devel</a><br>
            <a href="ftp://ftp.gnu.org/pub/gnu/make/">gnu
            make</a><br>
            <a href=
            "http://gcc.gnu.org/ml/libstdc++/2000-q2/msg00700/sstream">
            sstream</a><br>
          </td>

          <td>
            <a href=
            "http://www.rpmfind.net/linux/rpm2html/search.php?query=ruby">
            Ruby</a><br>
          </td>
        </tr>

        <tr>
          <td align="center"><b>Solaris</b></td>

          <td>
            <a href=
            "http://www.gnu.org/software/libc/#Availability">glibc</a><br>
          </td>

          <td>
            <a href=
            "http://www.gnu.org/software/gcc/releases.html">gcc</a>
            or SunPRO<br>
            gcc-c++<br>
            glibc-devel<br>

            <a href="ftp://ftp.gnu.org/pub/gnu/make/">gnu
            make</a><br>
            <a href=
            "http://gcc.gnu.org/ml/libstdc++/2000-q2/msg00700/sstream">
            sstream</a><br>
          </td>

          <td>
            <a href=
            "http://www.ruby-lang.org/en/download.html">Ruby</a><br>
            </td>
        </tr>

        <tr>
          <td align="center"><b>FreeBSD</b></td>

          <td>
          </td>

          <td>
          </td>

          <td>
            <a href="http://www.freebsd.org/ports/">Ruby<br></a>
          </td>
        </tr>

        <tr>
          <td align="center"><b>Mac OS X</b></td>

          <td>
          </td>

          <td>
            <a href=
            "http://developer.apple.com/tools/download/">Xcode
            Tools</a><br>
          </td>

          <td></td>
        </tr>
      </tbody>
    </table>

    <br/><hr/>
    <h1>Optional Dependencies</h1>

    <ul>
      <li>
        <a href="http://www.mysql.com/downloads/">MySQL</a>
      </li>

      <li>
        <a href=
        "http://www.microsoft.com/sql/default.mspx">MSSQL</a>
      </li>

      <li>
        <a href=
        "http://www.postgresql.org/download/">PostgreSQL</a>
      </li>
    </ul>
  </div>
</body>
</html>
