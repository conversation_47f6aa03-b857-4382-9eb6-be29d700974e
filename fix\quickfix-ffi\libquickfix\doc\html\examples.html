<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN">

<html>
<head>
  <link href="doxygen.css" rel="stylesheet" type="text/css">

  <title>Example Applications</title>
</head>

<body>
  <div class='header'>
    <div class='headertitle'>
      Example Applications
    </div>
  </div>

  <div class='contents'>
    <p>QuickFIX comes with several example applications in the
    <b>quickfix/examples</b> directory.</p>

    <p><b>ordermatch</b> is a c++ server that will match and
    execute limit orders</p>

    <p><b>tradeclient</b> is a little c++ console based trading
    client.</p>

    <p><b>banzai</b> is a Java GUI based trading
    client.</p><b>executor</b> is a server that fills every
    limit order it receives.

    <p>Executor has been implemented in <i>C++, Python</i> and
    <i>Ruby</i>.</p>
  </div>
</body>
</html>
