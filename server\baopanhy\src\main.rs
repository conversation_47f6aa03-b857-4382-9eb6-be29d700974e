// use server_hybaopan::*;
mod amiddleware;
mod api;
mod config;
mod dataservice;
mod globalevents;
mod server;
mod service;
pub use amiddleware::*;
pub use api::*;
pub use config::*;
pub use dataservice::*;
pub use globalevents::*;
pub use server::*;
pub use service::*;

use std::error::Error;
use std::net::SocketAddr;
use tokio::signal;
use tracing::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    let prefix = "hybaopan";
    let dir = "./logs";
    let _guard = server_initialize::initialize_tracing(prefix, dir);
    let setting = Settings::init("config/hybaopan.toml")?;
    info!("config: {:#?}", setting);

    // 1. 日志中心客户端初始化
    logclient::init_logclient(&setting.servers.logcenterserver, &format!("{}_{prefix}", "phoenix")).await;

    //运行服务
    let hyservice = HyService::init(setting.clone()).await?;
    hyservice.run().await?;

    //运行http服务
    let httpaddr = format!("{}:{}", setting.application.apphost, setting.application.httpport);
    info!("server running on {}", httpaddr);
    let listener = tokio::net::TcpListener::bind(&httpaddr).await.expect("bind error");

    let app = api::create_route(&setting.hyserver.allowlist, setting.hyserver.allowmethod);
    axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>())
        .with_graceful_shutdown(shutdown_signal())
        .await
        .unwrap();

    Ok(())
}
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
    };
    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };
    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();
    tokio::select! {
        _ = ctrl_c => {info!("Ctrl+C received, start to exit server")},
        _ = terminate => {},
    }
}
