//! `SeaORM` Entity, @generated by sea-orm-codegen 1.0.1

use sea_orm::entity::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>ult, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "sys_trade_channel")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub name: String,
    pub state: i32,
    pub create_time: i64,
    pub modify_time: i64,
    pub modify_name: String,
    pub inner_type: i8,
    pub qfstate: i8,
    pub unit_id: i64,
    pub channel_attr: i32,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
