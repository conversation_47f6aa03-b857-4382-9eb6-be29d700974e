use crate::{dataservice::entities::sys_trade_channel, dbsetup::DbConnection, entities::prelude::*};
use sea_orm::QueryFilter;
use sea_orm::entity::prelude::*;
use tracing::error;

impl SysTradeChannel {
    pub async fn query_many(channel_ids: &Vec<i64>, db: &DbConnection) -> Vec<SysTradeChannel> {
        let trade_channel = SysTradeChannelEntity::find()
            .filter(sys_trade_channel::Column::Id.is_in(channel_ids.to_vec()))
            .all(db.get_connection())
            .await;
        match trade_channel {
            Ok(trade_channel) => trade_channel,
            Err(e) => {
                error!("query_many error: {}", e);
                Vec::new()
            }
        }
    }
}
