use once_cell::sync::OnceCell;
// use quickfix::Message;
use quickfix_msg42::{ExecutionReport, OrderCancelReject};
use server_protoes::{ExecMsg, ExecType, MsgContent, MsgType, RouterMsg};
use tracing::{error, info};

pub static TRADEHANDLER: OnceCell<FixTradeRspHandler> = OnceCell::new();

#[derive(Clone, Debug)]
pub struct FixTradeRspHandler {
    pub tx_repay_to_router: tokio::sync::broadcast::Sender<RouterMsg>,
}

pub fn init_trade_handler(handler: &FixTradeRspHandler) {
    info!("初始化handler");
    TRADEHANDLER.set(handler.to_owned()).expect("初始化handler失败");
}

impl FixTradeRspHandler {
    /// 处理fix的响应消息
    pub fn process_fix_new_order_rsp(&self, msg: &ExecutionReport) {
        let mut resp = RouterMsg::default();
        let mut exec_msg = ExecMsg::default();
        let mut msg_content = MsgContent::default();

        // let cl_ord_id = msg.get_cl_ord_id().unwrap_or_default(); // 客户订单号

        let (channel_id, cl_ord_id) = match msg.get_cl_ord_id() {
            Some(id) => {
                if id.contains("-") {
                    let binding: Vec<&str> = id.split("-").collect();
                    (binding[0].parse().unwrap_or_default(), binding[1].parse().unwrap_or_default())
                } else {
                    (0, 0)
                }
            }
            None => (0, 0),
        };

        // 撤单使用
        let (_channel_id, orig_cl_ord_id) = match msg.get_orig_cl_ord_id() {
            Some(id) => {
                if id.contains("-") {
                    let binding: Vec<&str> = id.split("-").collect();
                    (binding[0].parse().unwrap_or_default(), binding[1].parse().unwrap_or_default())
                } else {
                    (0, 0)
                }
            }
            None => (0, 0),
        };

        info!("cl_ord_id: {}, orig_cl_ord_id: {}, channel_id: {}", cl_ord_id, orig_cl_ord_id, channel_id);

        let side = msg.get_side(); // 买卖方向
        let order_id = msg.get_order_id(); // 柜台合同号
        let last_px = msg.get_last_px(); // 最近一笔成交价格
        let last_shares = msg.get_last_shares(); // 最近一笔成交数量
        let order_qty = msg.get_order_qty(); // 委托数量
        let deal_qty = msg.get_cum_qty(); // 累计成交数量
        let exec_id = msg.get_exec_id(); // 执行编号
        let test = msg.get_text(); // 备注
        let avg_px = msg.get_avg_px(); // 成交均价
        // let exec_type = msg.get_exec_type(); // 执行类型

        // exec_msg.order_id = cl_ord_id;
        exec_msg.order_direction = side as i32;
        exec_msg.channel_id = channel_id;
        exec_msg.channel_type = 1;

        //订单状态
        match msg.get_ord_status() {
            quickfix_msg42::field_types::OrdStatus::New => {
                info!("orderstatus: {:?}", msg.get_ord_status());
                info!("委托下单确认 order_id: {}, channel_id: {}, confirm_on: {}", cl_ord_id, channel_id, order_id);
                exec_msg.exec_type = ExecType::Confirm as i32;
                exec_msg.order_id = cl_ord_id;
                exec_msg.brk_order_id = order_id.parse().unwrap_or_default();
            }
            quickfix_msg42::field_types::OrdStatus::Filled | quickfix_msg42::field_types::OrdStatus::PartiallyFilled => {
                info!("orderstatus: {:?}", msg.get_ord_status());
                info!(
                    "委托下单成交 order_id: {}, channel_id: {}, side: {:?}, 成交价格：{}, 本次成交数量：{}, 成交编号: {}, 本次成交均价: {}",
                    cl_ord_id,
                    channel_id,
                    side,
                    last_px.unwrap_or_default(),
                    last_shares.unwrap_or_default(),
                    exec_id,
                    avg_px
                );
                exec_msg.exec_type = ExecType::Filled as i32;
                exec_msg.order_id = cl_ord_id;
                exec_msg.exec_qty = last_shares.unwrap_or_default() as i32;
                exec_msg.exec_price = last_px.unwrap_or_default();
                exec_msg.exec_id = exec_id;
                exec_msg.exec_time = build_transact_time();
                exec_msg.brk_order_id = order_id.parse().unwrap_or_default();
            }
            quickfix_msg42::field_types::OrdStatus::Canceled => {
                info!("orderstatus: {:?}", msg.get_ord_status());
                info!("委托撤单成功 order_id: {}, channel_id: {}, side: {:?}", orig_cl_ord_id, channel_id, side);
                exec_msg.exec_type = ExecType::Canceled as i32;
                exec_msg.order_id = orig_cl_ord_id;
                exec_msg.exec_qty = (order_qty.unwrap_or_default() - deal_qty) as i32;
            }
            quickfix_msg42::field_types::OrdStatus::Rejected => {
                info!("orderstatus: {:?}", msg.get_ord_status());
                info!("委托下单拒绝 order_id: {}, channel_id: {}, side: {:?}", cl_ord_id, channel_id, side);
                exec_msg.exec_type = ExecType::Rejected as i32;
                exec_msg.order_id = if orig_cl_ord_id == 0 { cl_ord_id } else { orig_cl_ord_id };
                exec_msg.memo = test.unwrap_or_default();
            }
            quickfix_msg42::field_types::OrdStatus::PendingCancel => {
                info!("orderstatus: {:?}", msg.get_ord_status());
            }
            quickfix_msg42::field_types::OrdStatus::Expired => {
                info!("orderstatus: {:?}", msg.get_ord_status());
                info!("委托下单到期 order_id: {}, channel_id: {}, side: {:?}", cl_ord_id, channel_id, side);
                exec_msg.exec_type = ExecType::Rejected as i32;
                exec_msg.order_id = if orig_cl_ord_id == 0 { cl_ord_id } else { orig_cl_ord_id };
                exec_msg.memo = test.unwrap_or_default();
            }
            _ => {
                info!("orderstatus: {:?}", msg.get_ord_status());
            }
        }

        msg_content.exec_msg = Some(exec_msg);
        resp.msg_type = MsgType::Exec as i32;
        resp.msg_content = Some(msg_content);
        if let Err(e) = self.tx_repay_to_router.send(resp) {
            error!("发送消息到router失败: {:?}", e);
        }
    }

    /// 处理撤单拒绝
    pub fn process_fix_cancel_order_reject(&self, msg: &OrderCancelReject) {
        let mut resp = RouterMsg::default();
        let mut exec_msg = ExecMsg::default();
        let mut msg_content = MsgContent::default();

        let binding = msg.get_cl_ord_id();
        let channel_and_order_id: Vec<&str> = binding.split("-").collect();
        let channel_id = channel_and_order_id[0].parse().unwrap_or_default();
        let cancel_id = channel_and_order_id[1].parse().unwrap_or_default();

        let binding = msg.get_orig_cl_ord_id();
        let orig_channel_and_order_id: Vec<&str> = binding.split("-").collect();
        // let orig_channel_id = orig_channel_and_order_id[0].parse().unwrap_or_default();
        let order_id = orig_channel_and_order_id[1].parse().unwrap_or_default();

        // exec_msg.order_id = msg.get_orig_cl_ord_id().parse().unwrap_or_default();
        // exec_msg.cancel_id = msg.get_cl_ord_id().parse().unwrap_or_default();
        exec_msg.order_id = order_id;
        exec_msg.cancel_id = cancel_id;
        exec_msg.channel_id = channel_id;
        exec_msg.channel_type = 1;

        exec_msg.exec_type = ExecType::Rejected as i32;
        exec_msg.memo = msg.get_text().unwrap_or("".to_string());
        // info!("撤单拒绝消息: {:?}", );

        msg_content.exec_msg = Some(exec_msg);
        resp.msg_type = MsgType::Exec as i32;
        resp.msg_content = Some(msg_content);
        if let Err(e) = self.tx_repay_to_router.send(resp) {
            error!("发送消息到router失败: {:?}", e);
        }
    }
}

pub fn build_transact_time() -> String {
    let now = chrono::Utc::now();
    now.format("%Y%m%d-%T%.3f").to_string()
}

pub fn get_exchange_code(exchange_id: i32) -> String {
    // 101 =>  + "XSHG",
    // 102 =>  + "XSHE",
    // 103 =>  + "XHKG",        //港交所
    // 104 =>  + "XASE",        //美交所
    // 105 =>  + "XNYS",        //纽交所
    // 106 =>  + "XNAS",        //纳斯达克
    if exchange_id == 102 {
        return "XSHE".to_string();
    } else if exchange_id == 103 {
        return "XHKG".to_owned();
    } else if exchange_id == 104 {
        return "XASE".to_owned();
    } else if exchange_id == 105 {
        return "XNYS".to_owned();
    } else if exchange_id == 106 {
        return "XNAS".to_owned();
    }
    return "XSHG".to_owned(); //101
}

// pub fn process_fix_new_order_rsp(&self, msg: &ExecutionReport) {
//         let mut resp = RouterMsg::default();
//         let mut exec_msg = ExecMsg::default();
//         let mut msg_content = MsgContent::default();

//         // let cl_ord_id = msg.get_cl_ord_id().unwrap_or_default(); // 客户订单号

//         let (channel_id, cl_ord_id) = match msg.get_cl_ord_id() {
//             Some(id) => {
//                 if id.contains("-") {
//                     let binding: Vec<&str> = id.split("-").collect();
//                     (binding[0].parse().unwrap_or_default(), binding[1].parse().unwrap_or_default())
//                 } else {
//                     (0, 0)
//                 }
//             }
//             None => (0, 0),
//         };

//         // 撤单使用
//         let (_channel_id, orig_cl_ord_id) = match msg.get_orig_cl_ord_id() {
//             Some(id) => {
//                 if id.contains("-") {
//                     let binding: Vec<&str> = id.split("-").collect();
//                     (binding[0].parse().unwrap_or_default(), binding[1].parse().unwrap_or_default())
//                 } else {
//                     (0, 0)
//                 }
//             }
//             None => (0, 0),
//         };

//         info!("cl_ord_id: {}, orig_cl_ord_id: {}, channel_id: {}", cl_ord_id, orig_cl_ord_id, channel_id);

//         let side = msg.get_side(); // 买卖方向
//         let order_id = msg.get_order_id(); // 柜台合同号
//         let last_px = msg.get_last_px(); // 最近一笔成交价格
//         let last_shares = msg.get_last_shares(); // 最近一笔成交数量
//         let order_qty = msg.get_order_qty(); // 委托数量
//         let deal_qty = msg.get_cum_qty(); // 累计成交数量
//         let exec_id = msg.get_exec_id(); // 执行编号
//         let test = msg.get_text(); // 备注
//         let avg_px = msg.get_avg_px(); // 成交均价
//         // let exec_type = msg.get_exec_type(); // 执行类型

//         // exec_msg.order_id = cl_ord_id;
//         exec_msg.order_direction = side as i32;
//         exec_msg.channel_id = channel_id;
//         exec_msg.channel_type = 1;

//         //订单状态
//         match msg.get_ord_status() {
//             quickfix_msg42::field_types::OrdStatus::New => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//                 info!("委托下单确认 order_id: {}, channel_id: {}, confirm_on: {}", cl_ord_id, channel_id, order_id);
//                 exec_msg.exec_type = ExecType::Confirm as i32;
//                 exec_msg.order_id = cl_ord_id;
//                 exec_msg.brk_order_id = order_id.parse().unwrap_or_default();
//             }
//             // quickfix_msg42::field_types::OrdStatus::PartiallyFilled => {},
//             quickfix_msg42::field_types::OrdStatus::Filled | quickfix_msg42::field_types::OrdStatus::PartiallyFilled => {
//                 // | quickfix_msg42::field_types::OrdStatus::PendingCancel => {
//                 //     if exec_type == quickfix_msg42::field_types::ExecType::PartialFill || quickfix_msg42::field_types::ExecType::Fill
//                 {
//                     info!("orderstatus: {:?}", msg.get_ord_status());
//                     info!(
//                         "委托下单成交 order_id: {}, channel_id: {}, side: {:?}, 成交价格：{}, 本次成交数量：{}, 成交编号: {}, 本次成交均价: {}",
//                         cl_ord_id,
//                         channel_id,
//                         side,
//                         last_px.unwrap_or_default(),
//                         last_shares.unwrap_or_default(),
//                         exec_id,
//                         avg_px
//                     );
//                     exec_msg.exec_type = ExecType::Filled as i32;
//                     exec_msg.order_id = cl_ord_id;
//                     exec_msg.exec_qty = last_shares.unwrap_or_default() as i32;
//                     exec_msg.exec_price = last_px.unwrap_or_default();
//                     exec_msg.exec_id = exec_id;
//                     exec_msg.exec_time = build_transact_time();
//                     exec_msg.brk_order_id = order_id.parse().unwrap_or_default();
//                 }
//             }
//             quickfix_msg42::field_types::OrdStatus::DoneForDay => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::Canceled => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//                 info!("委托撤单成功 order_id: {}, channel_id: {}, side: {:?}", orig_cl_ord_id, channel_id, side);
//                 exec_msg.exec_type = ExecType::Canceled as i32;
//                 exec_msg.order_id = orig_cl_ord_id;
//                 exec_msg.exec_qty = (order_qty.unwrap_or_default() - deal_qty) as i32;
//             }
//             quickfix_msg42::field_types::OrdStatus::Replaced => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::PendingCancel => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::Stopped => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::Rejected => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//                 info!("委托下单拒绝 order_id: {}, channel_id: {}, side: {:?}", cl_ord_id, channel_id, side);
//                 exec_msg.exec_type = ExecType::Rejected as i32;
//                 exec_msg.order_id = if orig_cl_ord_id == 0 { cl_ord_id } else { orig_cl_ord_id };
//                 exec_msg.memo = test.unwrap_or_default();
//             }
//             quickfix_msg42::field_types::OrdStatus::Suspended => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::PendingNew => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::Calculated => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//             quickfix_msg42::field_types::OrdStatus::Expired => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//                 // info!("委托过期 orderstatus: {:?}", msg.get_ord_status()); //交易所拒绝
//             }
//             quickfix_msg42::field_types::OrdStatus::AcceptedForBidding => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//                 // info!(
//                 //     "委托撤单确认 order_id: {}, channel_id: {}, side: {:?}, 成交价格：{}, 本次成交数量：{}, 成交编号: {}, 本次成交均价: {}",
//                 //     cl_ord_id,
//                 //     channel_id,
//                 //     side,
//                 //     last_px.unwrap_or_default(),
//                 //     last_shares.unwrap_or_default(),
//                 //     exec_id,
//                 //     avg_px
//                 // );
//             }
//             quickfix_msg42::field_types::OrdStatus::PendingReplace => {
//                 info!("orderstatus: {:?}", msg.get_ord_status());
//             }
//         }

//         msg_content.exec_msg = Some(exec_msg);
//         resp.msg_type = MsgType::Exec as i32;
//         resp.msg_content = Some(msg_content);
//         if let Err(e) = self.tx_repay_to_router.send(resp) {
//             error!("发送消息到router失败: {:?}", e);
//         }
//     }

#[cfg(test)]
mod tests {
    use quickfix::*;

    #[test]
    fn test_create_message() {
        // 初始化一个FIX消息
        let mut message = Message::new();

        // 添加一个字段
        message.set_field(35, "D").unwrap(); // 35是消息类型字段，"D"表示NewOrderSingle

        // 验证消息类型是否正确设置
        assert_eq!(message.get_field(35).unwrap(), "D");
    }

    #[test]
    fn test_message_validation() {
        // 创建一个无效的FIX消息
        let mut message = Message::new();

        // 尝试设置一个无效的字段
        let result = message.set_field(9999, "INVALID");

        // 验证设置字段是否失败
        assert!(!result.is_err());
    }

    #[test]
    fn test_add_multiple_fields() {
        let mut message = Message::new();

        message.set_field(35, "D").unwrap(); // 设置消息类型
        message.set_field(49, "SENDER Comp ID").unwrap(); // 设置发送方公司ID
        message.set_field(56, "TARGET Comp ID").unwrap(); // 设置目标公司ID

        assert_eq!(message.get_field(35).unwrap(), "D");
        assert_eq!(message.get_field(49).unwrap(), "SENDER Comp ID");
        assert_eq!(message.get_field(56).unwrap(), "TARGET Comp ID");
    }

    #[test]
    fn test_remove_field() {
        let mut message = Message::new();

        message.set_field(35, "D").unwrap(); // 设置消息类型
        message.remove_field(35).unwrap(); // 移除消息类型

        assert!(message.get_field(35).is_none());
    }
}
