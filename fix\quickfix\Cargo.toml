[package]
name = "quickfix"
version = "0.1.6"
authors = ["Arthur LE MOIGNE"]
edition = "2021"
description = "High level binding to quickfix C++ library"
repository = "https://github.com/arthurlm/quickfix-rs"
license = "MIT OR Apache-1.1"
keywords = ["quickfix", "fix-protocol", "finance"]
categories = ["api-bindings", "finance"]
rust-version = "1.70.0"

[dependencies]
quickfix-ffi = { path = "../quickfix-ffi", version = "0.1.0" }
thiserror = "2.0.11"
log = { version = "0.4.22", optional = true }

[features]
default = ["log"]
build-with-mysql = ["quickfix-ffi/build-with-mysql"]
build-with-postgres = ["quickfix-ffi/build-with-postgres"]
log = ["dep:log"]
