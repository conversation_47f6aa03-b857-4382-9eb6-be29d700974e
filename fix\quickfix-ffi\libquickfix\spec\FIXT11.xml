<fix type='FIXT' major='1' minor='1' servicepack='0'>
 <header>
  <field name='BeginString' required='Y' />
  <field name='BodyLength' required='Y' />
  <field name='MsgType' required='Y' />
  <field name='ApplVerID' required='N' />
  <field name='ApplExtID' required='N' />
  <field name='CstmApplVerID' required='N' />
  <field name='SenderCompID' required='Y' />
  <field name='TargetCompID' required='Y' />
  <field name='OnBehalfOfCompID' required='N' />
  <field name='DeliverToCompID' required='N' />
  <field name='SecureDataLen' required='N' />
  <field name='SecureData' required='N' />
  <field name='MsgSeqNum' required='Y' />
  <field name='SenderSubID' required='N' />
  <field name='SenderLocationID' required='N' />
  <field name='TargetSubID' required='N' />
  <field name='TargetLocationID' required='N' />
  <field name='OnBehalfOfSubID' required='N' />
  <field name='OnBehalfOfLocationID' required='N' />
  <field name='DeliverToSubID' required='N' />
  <field name='DeliverToLocationID' required='N' />
  <field name='PossDupFlag' required='N' />
  <field name='PossResend' required='N' />
  <field name='SendingTime' required='Y' />
  <field name='OrigSendingTime' required='N' />
  <field name='XmlDataLen' required='N' />
  <field name='XmlData' required='N' />
  <field name='MessageEncoding' required='N' />
  <field name='LastMsgSeqNumProcessed' required='N' />
  <group name='NoHops' required='N'>
   <field name='HopCompID' required='N' />
   <field name='HopSendingTime' required='N' />
   <field name='HopRefID' required='N' />
  </group>
 </header>
 <messages>
  <message name='Heartbeat' msgtype='0' msgcat='admin'>
   <field name='TestReqID' required='N' />
  </message>
  <message name='TestRequest' msgtype='1' msgcat='admin'>
   <field name='TestReqID' required='Y' />
  </message>
  <message name='ResendRequest' msgtype='2' msgcat='admin'>
   <field name='BeginSeqNo' required='Y' />
   <field name='EndSeqNo' required='Y' />
  </message>
  <message name='Reject' msgtype='3' msgcat='admin'>
   <field name='RefSeqNum' required='Y' />
   <field name='RefTagID' required='N' />
   <field name='RefMsgType' required='N' />
   <field name='RefApplVerID' required='N' />
   <field name='RefApplExtID' required='N' />
   <field name='RefCstmApplVerID' required='N' />
   <field name='SessionRejectReason' required='N' />
   <field name='Text' required='N' />
   <field name='EncodedTextLen' required='N' />
   <field name='EncodedText' required='N' />
  </message>
  <message name='SequenceReset' msgtype='4' msgcat='admin'>
   <field name='GapFillFlag' required='N' />
   <field name='NewSeqNo' required='Y' />
  </message>
  <message name='Logout' msgtype='5' msgcat='admin'>
   <field name='SessionStatus' required='N' />
   <field name='Text' required='N' />
   <field name='EncodedTextLen' required='N' />
   <field name='EncodedText' required='N' />
  </message>
  <message name='Logon' msgtype='A' msgcat='admin'>
   <field name='EncryptMethod' required='Y' />
   <field name='HeartBtInt' required='Y' />
   <field name='RawDataLength' required='N' />
   <field name='RawData' required='N' />
   <field name='ResetSeqNumFlag' required='N' />
   <field name='NextExpectedMsgSeqNum' required='N' />
   <field name='MaxMessageSize' required='N' />
   <component name='MsgTypeGrp' required='N' />
   <field name='TestMessageIndicator' required='N' />
   <field name='Username' required='N' />
   <field name='Password' required='N' />
   <field name='NewPassword' required='N' />
   <field name='EncryptedPasswordMethod' required='N' />
   <field name='EncryptedPasswordLen' required='N' />
   <field name='EncryptedPassword' required='N' />
   <field name='EncryptedNewPasswordLen' required='N' />
   <field name='EncryptedNewPassword' required='N' />
   <field name='SessionStatus' required='N' />
   <field name='DefaultApplVerID' required='Y' />
   <field name='DefaultApplExtID' required='N' />
   <field name='DefaultCstmApplVerID' required='N' />
   <field name='Text' required='N' />
   <field name='EncodedTextLen' required='N' />
   <field name='EncodedText' required='N' />
  </message>
  <message name='XMLnonFIX' msgtype='n' msgcat='admin' />
 </messages>
 <trailer>
  <field name='SignatureLength' required='N' />
  <field name='Signature' required='N' />
  <field name='CheckSum' required='Y' />
 </trailer>
 <components>
  <component name='HopGrp'>
   <group name='NoHops' required='N'>
    <field name='HopCompID' required='N' />
    <field name='HopSendingTime' required='N' />
    <field name='HopRefID' required='N' />
   </group>
  </component>
  <component name='MsgTypeGrp' />
 </components>
 <fields>
  <field number='7' name='BeginSeqNo' type='SEQNUM' />
  <field number='8' name='BeginString' type='STRING' />
  <field number='9' name='BodyLength' type='LENGTH' />
  <field number='10' name='CheckSum' type='STRING' />
  <field number='16' name='EndSeqNo' type='SEQNUM' />
  <field number='34' name='MsgSeqNum' type='SEQNUM' />
  <field number='35' name='MsgType' type='STRING' />
  <field number='36' name='NewSeqNo' type='SEQNUM' />
  <field number='43' name='PossDupFlag' type='BOOLEAN'>
   <value enum='N' description='NO' />
   <value enum='Y' description='YES' />
  </field>
  <field number='45' name='RefSeqNum' type='SEQNUM' />
  <field number='49' name='SenderCompID' type='STRING' />
  <field number='50' name='SenderSubID' type='STRING' />
  <field number='52' name='SendingTime' type='UTCTIMESTAMP' />
  <field number='56' name='TargetCompID' type='STRING' />
  <field number='57' name='TargetSubID' type='STRING' />
  <field number='58' name='Text' type='STRING' />
  <field number='89' name='Signature' type='DATA' />
  <field number='90' name='SecureDataLen' type='LENGTH' />
  <field number='91' name='SecureData' type='DATA' />
  <field number='93' name='SignatureLength' type='LENGTH' />
  <field number='95' name='RawDataLength' type='LENGTH' />
  <field number='96' name='RawData' type='DATA' />
  <field number='97' name='PossResend' type='BOOLEAN'>
   <value enum='N' description='NO' />
   <value enum='Y' description='YES' />
  </field>
  <field number='98' name='EncryptMethod' type='INT'>
   <value enum='0' description='NONE_OTHER' />
   <value enum='1' description='PKCS' />
   <value enum='2' description='DES' />
   <value enum='3' description='PKCSDES' />
   <value enum='4' description='PGPDES' />
   <value enum='5' description='PGPDESMD5' />
   <value enum='6' description='PEMDESMD5' />
  </field>
  <field number='108' name='HeartBtInt' type='INT' />
  <field number='112' name='TestReqID' type='STRING' />
  <field number='115' name='OnBehalfOfCompID' type='STRING' />
  <field number='116' name='OnBehalfOfSubID' type='STRING' />
  <field number='122' name='OrigSendingTime' type='UTCTIMESTAMP' />
  <field number='123' name='GapFillFlag' type='BOOLEAN'>
   <value enum='N' description='NO' />
   <value enum='Y' description='YES' />
  </field>
  <field number='128' name='DeliverToCompID' type='STRING' />
  <field number='129' name='DeliverToSubID' type='STRING' />
  <field number='141' name='ResetSeqNumFlag' type='BOOLEAN'>
   <value enum='N' description='NO' />
   <value enum='Y' description='YES' />
  </field>
  <field number='142' name='SenderLocationID' type='STRING' />
  <field number='143' name='TargetLocationID' type='STRING' />
  <field number='144' name='OnBehalfOfLocationID' type='STRING' />
  <field number='145' name='DeliverToLocationID' type='STRING' />
  <field number='212' name='XmlDataLen' type='LENGTH' />
  <field number='213' name='XmlData' type='DATA' />
  <field number='347' name='MessageEncoding' type='STRING' />
  <field number='354' name='EncodedTextLen' type='LENGTH' />
  <field number='355' name='EncodedText' type='DATA' />
  <field number='369' name='LastMsgSeqNumProcessed' type='SEQNUM' />
  <field number='371' name='RefTagID' type='INT' />
  <field number='372' name='RefMsgType' type='STRING' />
  <field number='373' name='SessionRejectReason' type='INT'>
   <value enum='0' description='INVALID_TAG_NUMBER' />
   <value enum='1' description='REQUIRED_TAG_MISSING' />
   <value enum='2' description='TAG_NOT_DEFINED_FOR_THIS_MESSAGE_TYPE' />
   <value enum='3' description='UNDEFINED_TAG' />
   <value enum='4' description='TAG_SPECIFIED_WITHOUT_A_VALUE' />
   <value enum='5' description='VALUE_IS_INCORRECT' />
   <value enum='6' description='INCORRECT_DATA_FORMAT_FOR_VALUE' />
   <value enum='7' description='DECRYPTION_PROBLEM' />
   <value enum='8' description='SIGNATURE_PROBLEM' />
   <value enum='9' description='COMPID_PROBLEM' />
   <value enum='10' description='SENDINGTIME_ACCURACY_PROBLEM' />
   <value enum='11' description='INVALID_MSGTYPE' />
   <value enum='12' description='XML_VALIDATION_ERROR' />
   <value enum='13' description='TAG_APPEARS_MORE_THAN_ONCE' />
   <value enum='14' description='TAG_SPECIFIED_OUT_OF_REQUIRED_ORDER' />
   <value enum='15' description='REPEATING_GROUP_FIELDS_OUT_OF_ORDER' />
   <value enum='16' description='INCORRECT_NUMINGROUP_COUNT_FOR_REPEATING_GROUP' />
   <value enum='17' description='NON_DATA_VALUE_INCLUDES_FIELD_DELIMITER' />
   <value enum='18' description='INVALID_UNSUPPORTED_APPLICATION_VERSION' />
   <value enum='99' description='OTHER' />
  </field>
  <field number='383' name='MaxMessageSize' type='LENGTH' />
  <field number='464' name='TestMessageIndicator' type='BOOLEAN'>
   <value enum='N' description='NO' />
   <value enum='Y' description='YES' />
  </field>
  <field number='553' name='Username' type='STRING' />
  <field number='554' name='Password' type='STRING' />
  <field number='627' name='NoHops' type='NUMINGROUP' />
  <field number='628' name='HopCompID' type='STRING' />
  <field number='629' name='HopSendingTime' type='UTCTIMESTAMP' />
  <field number='630' name='HopRefID' type='SEQNUM' />
  <field number='789' name='NextExpectedMsgSeqNum' type='SEQNUM' />
  <field number='925' name='NewPassword' type='STRING' />
  <field number='1128' name='ApplVerID' type='STRING'>
   <value enum='0' description='FIX27' />
   <value enum='1' description='FIX30' />
   <value enum='2' description='FIX40' />
   <value enum='3' description='FIX41' />
   <value enum='4' description='FIX42' />
   <value enum='5' description='FIX43' />
   <value enum='6' description='FIX44' />
   <value enum='7' description='FIX50' />
   <value enum='8' description='FIX50_SP1' />
   <value enum='9' description='FIX50_SP2' />
  </field>
  <field number='1129' name='CstmApplVerID' type='STRING' />
  <field number='1130' name='RefApplVerID' type='STRING' />
  <field number='1131' name='RefCstmApplVerID' type='STRING' />
  <field number='1137' name='DefaultApplVerID' type='STRING' />
  <field number='1156' name='ApplExtID' type='INT' />
  <field number='1400' name='EncryptedPasswordMethod' type='INT' />
  <field number='1401' name='EncryptedPasswordLen' type='LENGTH' />
  <field number='1402' name='EncryptedPassword' type='DATA' />
  <field number='1403' name='EncryptedNewPasswordLen' type='LENGTH' />
  <field number='1404' name='EncryptedNewPassword' type='DATA' />
  <field number='1406' name='RefApplExtID' type='INT' />
  <field number='1407' name='DefaultApplExtID' type='INT' />
  <field number='1408' name='DefaultCstmApplVerID' type='STRING' />
  <field number='1409' name='SessionStatus' type='INT'>
   <value enum='0' description='SESSION_ACTIVE' />
   <value enum='1' description='SESSION_PASSWORD_CHANGED' />
   <value enum='2' description='SESSION_PASSWORD_DUE_TO_EXPIRE' />
   <value enum='3' description='NEW_SESSION_PASSWORD_DOES_NOT_COMPLY_WITH_POLICY' />
   <value enum='4' description='SESSION_LOGOUT_COMPLETE' />
   <value enum='5' description='INVALID_USERNAME_OR_PASSWORD' />
   <value enum='6' description='ACCOUNT_LOCKED' />
   <value enum='7' description='LOGONS_ARE_NOT_ALLOWED_AT_THIS_TIME' />
   <value enum='8' description='PASSWORD_EXPIRED' />
  </field>
 </fields>
</fix>