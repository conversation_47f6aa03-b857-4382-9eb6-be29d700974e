#!/bin/bash -l

set +e

# avoiding using any customizations in /usr/local on our buildslave
export PATH=/usr/bin:$PATH # prevent use of incompatible stuff in /usr/local on our buildslave

echo "debug info:"
cat /etc/issue
uname -a
id
rvm list

# prefer ruby-1.8 because we get 'Unable to send message because connection was dropped' errors with ruby-1.9
rvm use system || /bin/true
which ruby
ruby --version

