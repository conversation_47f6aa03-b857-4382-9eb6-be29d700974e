<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN">

<html>
<head>
  <link href="doxygen.css" rel="stylesheet" type="text/css">

  <title>Validation</title>
</head>

<body>
  <div class='header'>
    <div class='headertitle'>
      Validation
    </div>
  </div>

  <div class='contents'>
    <p>QuickFIX will validate and reject any poorly formed messages
    before they can reach your application. An XML file defines
    what messages, fields, and values a session supports.</p>

    <p>Several standard files are in included in the <b>spec</b>
    directory.</p>

    <p>The skeleton of a definition file looks like this.</p>
    <pre class='fragment'>
&lt;fix type="FIX" major="4" minor="1"&gt;
  &lt;header&gt;
    &lt;field name="BeginString" required="Y"/&gt;
    ...
  &lt;/header&gt;
  &lt;trailer&gt;
    &lt;field name="CheckSum" required="Y"/&gt;
    ...
  &lt;/trailer&gt;
  &lt;messages&gt;
    &lt;message name="Heartbeat" msgtype="0" msgcat="admin"&gt;
      &lt;field name="TestReqID" required="N"/&gt;
    &lt;/message&gt;
    ...
    &lt;message name="NewOrderSingle" msgtype="D" msgcat="app"&gt;
      &lt;field name="ClOrdID" required="Y"/&gt;
      ...
    &lt;/message&gt;
    ...
  &lt;/messages&gt;
  &lt;fields&gt;
    &lt;field number="1" name="Account" type="CHAR" /&gt;
    ...
    &lt;field number="4" name="AdvSide" type="CHAR"&gt;
     &lt;value enum="B" description="BUY" /&gt;
     &lt;value enum="S" description="SELL" /&gt;
     &lt;value enum="X" description="CROSS" /&gt;
     &lt;value enum="T" description="TRADE" /&gt;
   &lt;/field&gt;
   ...
  &lt;/fields&gt;
&lt;/fix&gt;
</pre>

    <p>The validator will not reject conditionally required fields
    because the rules for them are not clearly defined. Using a
    conditionally required field that is not preset will cause a
    message to be rejected.</p>
  </div>
</body>
</html>
