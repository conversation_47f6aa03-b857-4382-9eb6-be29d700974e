use crate::dataservice::{
    dbsetup::DbConnection,
    entities::prelude::{Syscommodity, SyscommodityEntity},
    entities::sys_commodity,
};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use tracing::*;
impl Syscommodity {
    pub async fn query_all(db: &DbConnection) -> Result<Vec<Syscommodity>, String> {
        let ret: Result<Vec<Syscommodity>, sea_orm::DbErr> = SyscommodityEntity::find()
            .filter(sys_commodity::Column::BusinessType.eq(3))
            .filter(sys_commodity::Column::Type.is_in(vec![1, 3]))
            .all(db.get_connection())
            .await;
        if ret.as_ref().is_err() {
            error!("phoenix_ord_stockorder_ext insert error,{:?}", ret);
            return Err("查询数据错误".to_string());
        }
        return Ok(ret.unwrap());
    }
}
