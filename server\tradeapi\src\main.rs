mod svrconfig;

mod apiserver;
mod connectionmanager;
mod dataservice;
mod ordercenterclient;

use mqclient::*;
// use server_api::*;
use server_constant::*;
use server_protoes::*;

use crate::apiserver::ApiServer;
use apiserver::*;
use connectionmanager::*;
use futures_util::{SinkExt, StreamExt};
use std::error::Error;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::signal;
use tokio::sync::{RwLock, mpsc};
use tokio_util::codec::{FramedRead, FramedWrite};
use tracing::*;

// 性能优化常量
const NOTIFICATION_CHANNEL_SIZE: usize = 512; // 减少通知通道大小
const TASK_CHANNEL_SIZE: usize = 64; // 减少任务通道大小
const CLIENT_CHANNEL_SIZE: usize = 32; // 减少客户端通道大小
const MQ_PROCESS_TIMEOUT_MS: u64 = 50; // MQ处理超时时间
const CLIENT_READ_TIMEOUT_SECS: u64 = 30; // 客户端读取超时时间
const CLIENT_WRITE_TIMEOUT_SECS: u64 = 15; // 客户端写入超时时间

#[tokio::main]
async fn main() -> std::result::Result<(), Box<dyn Error>> {
    // println!("Hello, world!");
    let _worker_guard = server_initialize::initialize_tracing("tradeapi", "./logs");

    let sys_setting = svrconfig::Settings::new("config/tradeapi.toml").expect("init settings error");

    server_initialize::initialize_event_channel().await;

    let (tx_notification, mut rx_notification) = tokio::sync::mpsc::channel::<NotificationMessage>(NOTIFICATION_CHANNEL_SIZE);

    //连接消息中心服务
    let queue_name = "phoenix_notification_client_queue_tradeapi";
    let notification_client = Arc::new(RwLock::new(
        NotificationClient::new(
            &sys_setting.mq.notification_exchanger.as_str(),
            queue_name,
            sys_setting.mq.accountriskcenter_routing_key.clone(),
            format!("{}{}", &sys_setting.mq.amqpaddr, &sys_setting.mq.vhost).as_str(),
            tx_notification,
        )
        .await,
    ));
    info!("初始化消息中心连接......");
    init_notification_listen(notification_client).await;

    let addr = format!("{}:{}", sys_setting.application.apphost, sys_setting.application.appport);
    // let addr = "0.0.0.0:8006";
    let apiserver = Arc::new(ApiServer::new(sys_setting).await);

    let listener = TcpListener::bind(&addr).await.expect("bind error");
    info!("server running on {}", addr);

    //任务通道
    let (task_tx, task_rx) = mpsc::channel::<Task>(TASK_CHANNEL_SIZE);
    // 初始化连接管理器
    let conn_manager = Arc::new(ConnectionManager::new());

    // 启动业务处理任务
    let conn_manager_clone: Arc<ConnectionManager> = conn_manager.clone();
    let apiserver_clone = Arc::clone(&apiserver);
    tokio::spawn(async move {
        apiserver_clone.run(task_rx, conn_manager_clone).await;
    });
    // tokio::spawn(business_logic(apiserver_clone,task_rx, conn_manager_clone));

    //启动mq客户端
    let conn_mgr = conn_manager.clone();
    tokio::spawn(async move {
        loop {
            tokio::select! {
                notify = rx_notification.recv() => {
                    if let Some(message) = notify {
                        // 使用 spawn 处理消息，避免阻塞主循环
                        let conn_mgr_clone = conn_mgr.clone();
                        let apiserver_clone = apiserver.clone();
                        tokio::spawn(async move {
                            if let Err(e) = handle_notification_message(message, conn_mgr_clone, apiserver_clone).await {
                                error!("处理通知消息失败: {}", e);
                            }
                        });
                    }
                }
                // 添加超时机制，避免无限等待
                _ = tokio::time::sleep(tokio::time::Duration::from_millis(MQ_PROCESS_TIMEOUT_MS)) => {
                    // 定期检查，但不做任何操作，只是为了避免忙等待
                    continue;
                }
            }
        }
    });

    // 主连接接受循环
    loop {
        let accept_result = tokio::select! {
            // Either accept a new connection...
            result = listener.accept() => {
                result
            }
            // ...or wait to receive a shutdown signal and stop the accept loop.
            _ = shutdown_signal() => {
                info!("shutdown signal received, will not accepting any new connections");
                break;
            }
        };

        match accept_result {
            Ok((stream, socketaddr)) => {
                info!("client connected: {:?}", socketaddr);

                // 为每个客户端创建通信通道，使用较小的缓冲区以减少内存使用
                let (client_tx, client_rx) = mpsc::channel::<TaMessageRespPack>(CLIENT_CHANNEL_SIZE);
                let addr_str = socketaddr.to_string();

                // 注册客户端连接
                conn_manager.add_client(addr_str.clone(), client_tx).await;

                // 启动客户端处理任务
                let task_tx_clone = task_tx.clone();
                let conn_manager_clone = conn_manager.clone();
                tokio::spawn(async move {
                    handle_client(stream, addr_str, task_tx_clone, client_rx, conn_manager_clone).await;
                });
            }
            Err(e) => {
                error!("Failed to accept connection: {}", e);
                // 短暂延迟后继续，避免快速失败循环
                tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            }
        }
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
    }
    drop(listener);
    info!("server exited successfully");
    Ok(())
}

async fn handle_client(
    stream: TcpStream,
    addr: String,
    task_tx: mpsc::Sender<Task>,
    mut rx: mpsc::Receiver<TaMessageRespPack>,
    connmgr: Arc<ConnectionManager>,
) {
    let (rd, wr) = stream.into_split();
    // 将TcpStream转换为Framed
    let mut frame_reader = FramedRead::new(rd, ReqCodec::new());
    let mut frame_writer = FramedWrite::new(wr, RespCodec::new());

    let addr_clone_1 = addr.clone();
    // let addr_str = addr.as_str();
    //负责返回客户端数据的任务
    tokio::spawn(async move {
        loop {
            tokio::select! {
                resp = rx.recv() => {
                    match resp {
                        Some(resp) => {
                            info!("response package to client #{}", addr_clone_1);
                            if let Err(e) = frame_writer.send(resp).await {
                                error!("write to client failed: {:?}", e);
                                break; // 连接断开时退出循环
                            }
                        }
                        None => {
                            info!("client #{} response channel closed", addr_clone_1);
                            break;
                        }
                    }
                }
                // 添加超时机制，避免长时间阻塞
                _ = tokio::time::sleep(tokio::time::Duration::from_secs(CLIENT_WRITE_TIMEOUT_SECS)) => {
                    // 定期检查连接状态，但不做任何操作
                    continue;
                }
            }
        }
    });

    // 负责读客户端的异步子任务
    tokio::spawn(async move {
        loop {
            tokio::select! {
                frame_result = frame_reader.next() => {
                    match frame_result {
                        None => {
                            info!("client #{} closed", &addr);
                            // 清理断开的客户端
                            connmgr.remove_client(&addr).await;
                            break;
                        }
                        Some(Err(e)) => {
                            error!("read from client #{} error: {}", &addr, e);
                            connmgr.remove_client(&addr).await;
                            break;
                        }
                        Some(Ok(req)) => {
                            info!("read from client #{}", &addr);

                            // 创建一个虚拟的响应通道，因为实际响应通过连接管理器发送
                            let (resp_tx, _resp_rx) = mpsc::channel::<TaMessageRespPack>(1);
                            let task = Task {
                                request: req,
                                sender: resp_tx,
                                client_addr: addr.clone(),
                            };

                            if let Err(_) = task_tx.send(task).await {
                                error!("Business logic task channel closed for client #{}", &addr);
                                break;
                            }
                        }
                    }
                }
                // 添加超时机制，避免长时间阻塞读取
                _ = tokio::time::sleep(tokio::time::Duration::from_secs(CLIENT_READ_TIMEOUT_SECS)) => {
                    // 定期检查，避免死锁
                    continue;
                }
            }
        }
    });
}

// 处理通知消息的独立函数
async fn handle_notification_message(
    message: NotificationMessage,
    conn_mgr: Arc<ConnectionManager>,
    apiserver: Arc<ApiServer>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    if let Some(message_body) = message.msg_body {
        let mut clientid = String::new();
        let mut push_state = false;

        let mut head = TaMessageHeader {
            begin_string: BEGIN_STRING.to_string(),
            message_type: MessageType::Undefined as i32,
            sender_compid: String::new(),
            sending_time: utils::current_timestamp(),
            client_id: String::new(),
        };

        let mut orderinfo = None::<TaOrderExecResp>;

        match message.msg_type {
            x if x == (NotificationType::UserOrderInfo as i32) => {
                info!("订单执行回报:{:?}", &message_body);

                if let Some(orderexec) = message_body.msg_orderinfo {
                    clientid = orderexec.unit_id.to_string();
                    push_state = conn_mgr.exists_client(&clientid).await;

                    if orderexec.order_status == OrdStatus::OrdStatusINITED as i32 {
                        push_state = false;
                    }

                    // 订单未报状态不处理。默认不推送给报盘
                    if push_state {
                        head.message_type = MessageType::OrderExec as i32;
                        head.client_id = clientid.clone();
                        let cl_ord_id = apiserver.query_orderno(orderexec.order_no).await;
                        let mut data = TaOrderExecResp::from(orderexec);
                        data.cl_ord_id = cl_ord_id;
                        orderinfo = Some(data);
                    }
                }
            }
            _ => {}
        }

        if head.message_type != MessageType::Undefined as i32 && orderinfo.is_some() && !clientid.is_empty() && push_state {
            let resp = TaMessageRespPack {
                header: Some(head),
                orderexec: orderinfo,
                resp: None,
            };
            if let Err(e) = conn_mgr.send_to_client_id(&clientid, resp).await {
                error!("发送给客户端错误: {}", e);
            }
        }
    }
    Ok(())
}

async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c().await.expect("failed to install Ctrl+C handler");
    };
    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };
    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();
    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }
}

// 业务处理逻辑
// async fn business_logic(apiserver: Arc<ApiServer>, mut rx: mpsc::Receiver<Task>, conn_manager: Arc<ConnectionManager>) {
//     while let Some(task) = rx.recv().await {
//         info!("Processing from {}", /*task.request.header, task,*/ task.client_addr);

//         //这里处理具体的业务逻辑
//         info!("我在努力处理业务逻辑......");
//         tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
//         let _ = apiserver.handle_order_req(&task.request);
//         info!("业务处理完成，本次相应将发送给{}",&task.client_addr);

//         let mut response = TaMessageRespPack::default();
//         let header = TaMessageHeader{begin_string:task.client_addr.to_string(),..Default::default()};
//         response.header = Some(header);

//         // 发送响应给特定客户端
//         if let Err(e) = conn_manager.send_to_client(&task.client_addr, response).await {
//             error!("Failed to send response to {}: {}", task.client_addr, e);
//         }
//     }
// }

// async fn process_client(stream: TcpStream,clientid:u32,msg_tx:mpsc::Sender<TaMessageRespPack>, mut rx: mpsc::Receiver<TaMessageRespPack>, apiserver: &ApiServer) {
//     let (rd, wr) = stream.into_split();
//     // 将TcpStream转换为Framed
//     let mut frame_reader = FramedRead::new(rd, ReqCodec);
//     let mut frame_writer = FramedWrite::new(wr, RespCodec);

//     // 当Reader从客户端读取到数据后，发送到通道中，
//     // 另一个异步任务读取该通道，从通道中读取到数据后，将内容写给客户端
//     // let (msg_tx, msg_rx) = mpsc::channel::<TaMessageRespPack>(200);
//     let apiserver_clone = apiserver.clone();

//     //负责返回客户端数据的任务
//     tokio::spawn(async move{
//         loop{
//             tokio::select! {
//                 resp = rx.recv()=>{
//                     if let Some(resp) = resp{
//                         info!("response package to client #{} {:?}", clientid,&resp);
//                         // send_response(&mut frame_writer, resp).await;
//                         if frame_writer.send(resp).await.is_err() {
//                             error!("write to client failed");
//                             // break;
//                         }
//                     }
//                 }
//             }
//         }
//     });

//     // 负责读客户端的异步子任务
//     tokio::spawn(async move {
//         // handle_request(frame_reader, msg_tx, clientid, &apiserver_clone).await;
//         loop {
//             match frame_reader.next().await {
//                 None => {
//                     debug!("client closed");
//                     // 清理断开的客户端
//                     // let mut clients_lock = clients_clone.lock().await;
//                     let mut clients_lock = GLOBAL_CLIENTS_CACHE.write().await;
//                     clients_lock.remove(&clientid);
//                     info!( clientid, "Client disconnected");
//                     break;
//                 }
//                 Some(Err(e)) => {
//                     error!("read from client error: {}", e);
//                     break;
//                 }
//                 Some(Ok(req)) => {
//                     info!("read from client #{}. content: {:?}",clientid, req);

//                     // let _resp = apiserver_clone.handle_order_req(&req,msg_tx.clone()).await;
//                     // 将内容发送给writer，让writer响应给客户端，
//                     // 如果无法发送给writer，继续从客户端读取内容将没有意义，因此break退出
//                     // if msg_tx.send(resp).await.is_err() {
//                     //     error!("receiver closed");
//                     // }
//                     // info!("start to send global events......");
//                     // global::send_string_event("msg".to_string());
//                 }
//             }
//         }
//     });

// }

// async fn handle_request<T: AsyncReadExt + Unpin>(
//     mut reader: FramedRead<T, ReqCodec>,
//     msg_tx: mpsc::Sender<TaMessageRespPack>,
//     clientid:u32,
//     apiserver: &ApiServer,
// ) {
//     loop {
//         match reader.next().await {
//             None => {
//                 debug!("client closed");
//                 // 清理断开的客户端
//                 // let mut clients_lock = clients_clone.lock().await;
//                 let mut clients_lock = GLOBAL_CLIENTS_CACHE.write().await;
//                 clients_lock.remove(&clientid);
//                 info!( clientid, "Client disconnected");
//                 break;
//             }
//             Some(Err(e)) => {
//                 error!("read from client error: {}", e);
//                 break;
//             }
//             Some(Ok(msg)) => {
//                 info!("read from client #{}. content: {:?}",clientid, msg);
//                 // let _resp = apiserver.handle_order_req(&msg,msg_tx.clone()).await;
//                 // 将内容发送给writer，让writer响应给客户端，
//                 // 如果无法发送给writer，继续从客户端读取内容将没有意义，因此break退出
//                 // if msg_tx.send(resp).await.is_err() {
//                 //     error!("receiver closed");
//                 // }
//                 // info!("start to send global events......");
//                 // global::send_string_event("msg".to_string());
//             }
//         }
//     }
// }

// async fn send_response<T: AsyncWriteExt + Unpin>(
//     writer: &mut FramedWrite<T, RespCodec>,
//     resp:TaMessageRespPack,
//     // mut msg_rx: mpsc::Receiver<TaMessageRespPack>,
// ) {
//     // while let Some(resp) = msg_rx.recv().await {
//         info!("msg received......{:?}",resp);
//     //     let mut header = TaMessageHeader::default();
//     //     header.message_type = 1;
//     //     let resp = TaMessageResp {
//     //         error_code: 0,
//     //         error_msg: "dkdkkdkdkdkd合理使用".to_string(),
//     //     };
//     //     let mut resppack = TaMessageRespPack::default();
//     //     resppack.header = Some(header);
//     //     resppack.resp = Some(resp);
//         if writer.send(resp).await.is_err() {
//             error!("write to client failed");
//             // break;
//         }
//     // }
// }
