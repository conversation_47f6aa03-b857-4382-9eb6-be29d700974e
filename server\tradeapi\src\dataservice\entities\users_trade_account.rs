//! `SeaORM` Entity. Generated by sea-orm-codegen 0.11.0

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(table_name = "users_trade_account")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub account_no: String,
    pub pwd: String,
    pub user_id: i64,
    pub trade_id: Option<i64>,
    pub agent_id: i64,
    pub user_organ_code: String,
    pub sale_user_id: i64,
    pub create_time: i64,
    pub status: i8,
    pub business_type: i8,
    pub currency: String,
    pub online: i8,
    pub last_login_time: i64,
    pub open_source: i8,
    pub bi_no: String,
    pub infomation_complate: i8,
    pub into_ctrl: i8,
    pub withdraw_ctrl: i8,
    pub transfer_ctrl: i8,
    pub organ_code: Option<String>,
    pub account_type: i8,
    pub account_cate: i8,
    pub crmgroup_id: i32,
    pub grant_login: i8,
    pub trade_channel: Option<i8>,
    pub trade_type: i8,
    pub counterparty: i8,
    pub organ_user_id: i64,
    pub rq_state: i8,
    pub rq_level: i32,
    pub rq_account_type: i32,
    pub algorithm_state: i8,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
