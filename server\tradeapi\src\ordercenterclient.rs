use server_protoes::{CancelReq, OrderReq, order_center_service_client::*};
use std::sync::Arc;
use tokio::sync::RwLock;
use tonic::{Result, transport::Channel};
use tracing::*;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct OrderCenterClient {
    orderclient: Arc<RwLock<Option<OrderCenterServiceClient<Channel>>>>,
    uri: String,
}

impl OrderCenterClient {
    pub async fn init(url: String) -> Self {
        info!(url, "订单中心");

        let orderclient = Arc::new(RwLock::new(None::<OrderCenterServiceClient<Channel>>));

        let oclient = OrderCenterServiceClient::connect(url.clone()).await;
        if oclient.is_ok() {
            info!("订单中心连接成功");
            let mut wr = orderclient.write().await;
            *wr = Some(oclient.unwrap());
        }

        let client = OrderCenterClient { orderclient, uri: url };

        client.start_reconnect_listen().await; // Start the reconnection loop

        client
    }

    pub async fn start_reconnect_listen(&self) {
        let orderclient_clone = Arc::clone(&self.orderclient);
        let url = self.uri.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = tokio::time::sleep(std::time::Duration::from_secs(3)) => {
                        if orderclient_clone.read().await.is_none() {
                            info!("订单中心重新连接中......");
                            let client = OrderCenterServiceClient::connect(url.clone()).await;
                            if client.is_err() {
                                error!("不能连接到订单中心，3秒后重连");
                                // std::thread::sleep(std::time::Duration::from_secs(3));
                                // continue;
                            }else{
                                info!("订单中心连接成功......");
                                let mut wr = orderclient_clone.write().await;
                                *wr = Some(client.unwrap());
                            }
                        }
                    }
                }
            }
        });
    }

    pub async fn send_order(&self, req: &OrderReq) -> Result<i64, String> {
        if self.orderclient.read().await.is_none() {
            error!("ordercenter is not connected！");
            // self.reconnect().await;
            return Err("ordercenter is not connected！".to_string());
        };
        let ret = self.orderclient.write().await.as_mut().unwrap().place_order(req.to_owned()).await;

        if ret.is_err() {
            error!("place_order error{:?}", ret);
            let mut wr = self.orderclient.write().await;
            *wr = None;
            // self.reconnect().await;
            return Err("order failed！".to_string());
        }
        let ret = ret.unwrap();

        info!("send_order...{:?}", ret);

        let ret = ret.into_inner();
        if ret.error_code != 0 {
            return Err(ret.error_msg);
        }
        let order_id = ret.order_id;
        Ok(order_id)
    }

    pub async fn cancel_order(&self, req: &CancelReq) -> Result<i32, String> {
        if self.orderclient.read().await.is_none() {
            error!("ordercenter is not connected！");
            return Err("ordercenter is not connected！".to_string());
        };
        let ret = self.orderclient.write().await.as_mut().unwrap().cancel_order(req.to_owned()).await;
        if ret.is_err() {
            error!("cancel_order error{:?}", ret);
            let mut wr = self.orderclient.write().await;
            *wr = None;
            // self.reconnect().await;
            return Err("cancel order failed！".to_string());
        }
        Ok(0)
    }
}
