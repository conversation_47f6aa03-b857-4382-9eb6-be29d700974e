mod fixclient;
pub use fixclient::*;

mod server;
pub use server::*;

mod config;
pub use config::*;

mod controller;
pub use controller::*;

mod service;
pub use service::*;

mod dataservice;
pub use dataservice::*;

// pub fn add(left: u64, right: u64) -> u64 {
//     left + right
// }

// #[cfg(test)]
// mod tests {
//     use super::*;

//     #[test]
//     fn it_works() {
//         let result = add(2, 2);
//         assert_eq!(result, 4);
//     }
// }
