解决 Centos 运行错误：/lib64/libstdc++.so.6: version `GLIBCXX_3.4.29' not found


在CentOS上编译升级C++编译器（GCC）并配置好环境变量，可以按照以下步骤进行：


### 1. 检查系统信息

首先，确认您的系统信息，包括当前GCC版本。可以通过以下命令查看GCC版本：

gcc --version


### 2. 安装依赖包

在安装GCC之前，需要确保系统上安装了所有必要的依赖包。这些包可能包括 `gcc-c++`、`gcc-gfortran`等。您可以使用如下命令安装常见的依赖：

sudo yum groupinstall "Development Tools"


### 3. 下载GCC源代码

访问GCC的官方网站下载最新的GCC版本源代码。您也可以使用 `wget`命令直接在终端中下载：

wget http://ftp.gnu.org/gnu/gcc/gcc-`<version>`/gcc-`<version>`.tar.gz

将 `<version>`替换为您想要下载的GCC版本号。


### 4. 解压和编译GCC

解压下载的GCC源代码，并按照说明进行编译和安装。以下是一个基本的编译安装流程：

tar -xzf gcc-`<version>`.tar.gz
cd gcc-`<version>`
./contrib/download_prerequisites
mkdir build
cd build
../configure --enable-languages=c,c++ --disable-multilib
make -j$(nproc)
sudo make install

在这里，`--enable-languages=c,c++`选项用于启用C和C++编译器。`--disable-multilib`选项用于禁用多架构支持，这可以减少编译时间，但可能不适用于所有用户。


### 5. 配置环境变量

编译安装完成后，您需要配置环境变量来使用新的GCC版本。打开您的shell配置文件（例如 `~/.bashrc`或 `~/.bash_profile`），并添加以下行：

export CC=/usr/local/bin/gcc
export CXX=/usr/local/bin/g++
export PATH=/usr/local/bin:$PATH


这些命令将新的GCC和G++可执行文件的路径添加到您的 `PATH`环境变量中，并设置 `CC`和 `CXX`环境变量以指示编译器使用这些新版本。

保存文件并退出编辑器。然后，运行以下命令以使更改生效：

source ~/.bashrc


### 6. 验证安装

最后，再次运行 `gcc --version`和 `g++ --version`来确认GCC和G++是否已成功升级：


gcc --version
g++ --version


**创建符号链接** ：

如果已经安装了正确的GCC版本，但问题仍然存在，可以尝试创建一个新的符号链接指向新的 `libstdc++.so.6`库。首先，找到新的 `libstdc++.so.6`文件，它通常位于 `/usr/local/lib64/`或 `/opt/gcc-<version>/lib64/`目录下。然后，创建符号链接：

sudo ln -sf /usr/local/lib64/libstdc++.so.6 /usr/lib64/libstdc++.so.6


请将 `/usr/local/lib64/libstdc++.so.6`替换为实际的新库文件路径。

**使用LD_LIBRARY_PATH** ：

不想更改系统级别的符号链接，您也可以通过设置 `LD_LIBRARY_PATH`环境变量来临时使用新的库文件。在运行程序之前，执行以下命令：

export LD_LIBRARY_PATH=/usr/local/lib64:$LD_LIBRARY_PATH

或者添加到 bashrc中
