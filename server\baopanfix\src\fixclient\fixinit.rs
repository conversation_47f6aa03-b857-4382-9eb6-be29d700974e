use quickfix::*;
use std::{sync::Arc, thread, time::Duration};
use tracing::error;

use crate::{TFixTradeClient, YhFixTradeClient};

//根据实际调整
pub enum ClientType {
    /// temp
    TFix = 0,
    /// 银河
    YhFix = 23,
    YhFixQFII = 24,
    /// 广发
    GfFix = 38,
    GfFixQFII = 39,
}

pub trait FixClientTrait {
    fn run(&self) {
        loop {
            thread::sleep(Duration::from_secs(10));
        }
    }

    /// Starts the FIX client with the provided configuration file.
    fn start(&self, config_file: &str) -> Result<(), Box<dyn std::error::Error>>;
}

/// quickfix 启动客户端
pub fn start_client<T: ApplicationCallback + FixClientTrait + Send + Sync + 'static>(client: &Arc<T>, config_file: &str) {
    let client = client.clone();
    let config_file = config_file.to_string();
    tokio::task::spawn_blocking(move || {
        if let Err(err) = client.start(&config_file) {
            error!("Error starting client: {}", err);
        }
    });
    // tokio::spawn(async move {
    //     if let Err(err) = client.start(&config_file) {
    //         error!("Error starting client: {}", err);
    //     }
    // });
}

#[derive(Default)]
pub struct FixTradeSer;

impl FixTradeSer {
    /// Initializes a FIX client with the provided configuration file.
    /// 泛型函数，初始化一个fix客户端
    pub fn init_fix_client<C: ApplicationCallback + 'static + FixClientTrait>(fix_client: &C, config_file: &str) -> anyhow::Result<()> {
        let settings = SessionSettings::try_from_path(config_file)?;
        let store_factory = FileMessageStoreFactory::try_new(&settings)?;
        let log_factory = LogFactory::try_new(&StdLogger::Stdout)?;
        let app = Application::try_new(fix_client)?;

        let mut initiator = SocketInitiator::try_new(&settings, &app, &store_factory, &log_factory)?;

        initiator.start()?;
        fix_client.run();
        initiator.stop()?;
        Ok(())
    }

    pub fn init_tfix(fix_client: &TFixTradeClient, config_file: &str) -> anyhow::Result<()> {
        let settings = SessionSettings::try_from_path(config_file)?;
        let store_factory = FileMessageStoreFactory::try_new(&settings)?;
        let log_factory = LogFactory::try_new(&StdLogger::Stdout)?;
        let app = Application::try_new(fix_client)?;

        let mut initiator = SocketInitiator::try_new(&settings, &app, &store_factory, &log_factory)?;

        initiator.start()?;
        fix_client.run();
        initiator.stop()?;
        Ok(())
    }

    pub fn init_yhtfix(fix_client: &YhFixTradeClient, config_file: &str) -> anyhow::Result<()> {
        let settings = SessionSettings::try_from_path(config_file)?;
        let store_factory = FileMessageStoreFactory::try_new(&settings)?;
        let log_factory = LogFactory::try_new(&StdLogger::Stdout)?;
        let app = Application::try_new(fix_client)?;

        let mut initiator = SocketInitiator::try_new(&settings, &app, &store_factory, &log_factory)?;

        initiator.start()?;
        fix_client.run();
        initiator.stop()?;
        Ok(())
    }
}
