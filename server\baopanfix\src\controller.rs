// use lazy_static::lazy_static;
use server_protoes::OrderMsg;
use std::sync::Arc;

use crate::{ClientType, GfFixTradeClient, TFixTradeClient, YhFixTradeClient};

// lazy_static! {
//     pub static ref CHANNEL_INFO: RwLock<HashMap<i64, SysTradeChannel>> = RwLock::new(HashMap::new());
// }

#[derive(Debug, PartialEq)]
pub enum Qfstate {
    Yes = 1,
    No = 2,
}

// pub async fn init_channel_info(channel: Vec<SysTradeChannel>) {
//     info!("初始化channel_info: {:#?}", channel);
//     let mut channel_info = CHANNEL_INFO.write().await;
//     channel.iter().for_each(|x| {
//         channel_info.insert(x.id, x.clone());
//     });
// }

pub struct TradeController {
    pub tfix: Arc<TFixTradeClient>,
    pub yhfix: Arc<YhFixTradeClient>,
    pub gffix: Arc<GfFixTradeClient>,
}

impl TradeController {
    pub fn send_fix(&self, request: OrderMsg) {
        match request.order_type() {
            server_protoes::OrderType::Place => self.place_order(request),
            server_protoes::OrderType::Cancel => self.cancel_order(request),
            _ => {}
        }
    }

    pub fn place_order(&self, request: OrderMsg) {
        //根据不同的channel_id，往不同的上手去报
        match request.channel_id {
            x if x == ClientType::TFix as i64 => {
                let _ = self.tfix._req_new_single_order(&request);
            }
            x if x == ClientType::YhFix as i64 || x == ClientType::YhFixQFII as i64 => {
                let _ = self.yhfix._req_new_single_order(&request);
            }
            x if x == ClientType::GfFix as i64 || x == ClientType::GfFixQFII as i64 => {
                let _ = self.gffix._req_new_single_order(&request);
            }
            _ => {}
        }
    }

    pub fn cancel_order(&self, request: OrderMsg) {
        //根据不同的channel_id，往不同的上手去报
        match request.channel_id {
            x if x == ClientType::TFix as i64 => {
                let _ = self.tfix._req_order_cancel(&request);
            }
            x if x == ClientType::YhFix as i64 || x == ClientType::YhFixQFII as i64 => {
                let _ = self.yhfix._req_order_cancel(&request);
            }
            x if x == ClientType::GfFix as i64 || x == ClientType::GfFixQFII as i64 => {
                let _ = self.gffix._req_order_cancel(&request);
            }
            _ => {}
        }
    }
}
