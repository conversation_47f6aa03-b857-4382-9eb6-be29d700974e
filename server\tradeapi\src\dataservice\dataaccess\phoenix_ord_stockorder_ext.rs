use crate::{
    dataservice::entities::phoenix_ord_stockorder_ext,
    dataservice::{
        dbsetup::DbConnection,
        entities::prelude::{PhoenixOrdStockorderExt, PhoenixOrdStockorderExtEntity},
    },
};
use sea_orm::{ActiveModelTrait, ColumnTrait, EntityTrait, IntoActiveModel, QueryFilter};
use tracing::*;
impl PhoenixOrdStockorderExt {
    pub async fn insert_data(data: PhoenixOrdStockorderExt, db: &DbConnection) -> Result<i64, String> {
        let newval = data.to_owned().into_active_model();
        let ret = newval.insert(db.get_connection()).await;
        if ret.as_ref().is_err() {
            error!("phoenix_ord_stockorder_ext insert error,{:?}", ret);
            return Err("数据插入异常".to_string());
        }
        let id = ret.unwrap().id;
        return Ok(id);
    }

    pub async fn query(orderno: i64, db: &DbConnection) -> Result<Option<PhoenixOrdStockorderExt>, String> {
        let ret = PhoenixOrdStockorderExtEntity::find()
            .filter(phoenix_ord_stockorder_ext::Column::OrderId.eq(orderno))
            .one(db.get_connection())
            .await;

        if ret.as_ref().is_err() {
            error!("phoenix_ord_stockorder_ext query error,{:?}", ret);
            return Err("数据查询异常".to_string());
        }

        let ret = ret.unwrap();
        return Ok(ret);
    }
}
