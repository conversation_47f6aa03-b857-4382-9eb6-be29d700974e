<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN">

<html>
<head>
  <link href="doxygen.css" rel="stylesheet" type="text/css">

  <title>Testing QuickFIX</title>
</head>

<body>
  <div class='header'>
    <div class='headertitle'>
      Testing QuickFIX
    </div>
  </div>

  <div class='contents'>
    <p>The development of QuickFIX has been driven by a suite of
    functional acceptance tests and unit tests.</p>

    <hr/>
    <h1>Windows</hq>

    <h2>Unit Tests</h2>

    <p>From the <b>test</b> directory:</p>
    <pre class='fragment'>
runut release [port]
</pre>

    <p>The port is used to test socket functionality. If you built
    QuickFIX with one of its supporting databases, you will need to
    update <i>cfg/ut.cfg</i> to reflect your database settings.</p>

    <h2>Acceptance Tests</h2>

    <p>From the <b>test</b> directory:</p>
    <pre class='fragment'>
runat release [port]
</pre>
    <pre class='fragment'>
runat_threaded release [port]
</pre>

    <p>The port is used to listen for connections on a socket
    server.</p>

    <br/><hr/>
    <h1>Linux / Solaris / FreeBSD / Mac OS X</h1>

    <h2>Unit Tests</h2>

    <p>From the <b>test</b> directory:</p>
    <pre class='fragment'>
./runut.sh [port]
</pre>

    <p>The port is used to test socket functionality. If you built
    QuickFIX with one of its supporting databases, you will need to
    update <i>cfg/ut.cfg</i> to reflect your database settings.</p>

    <h2>Acceptance Tests</h2>

    <p>From the <b>test</b> directory:</p>
    <pre class='fragment'>
./runat.sh [port]
</pre>
    <pre class='fragment'>
./runat_threaded.sh [port]
</pre>

    <p>The port is used to listen for connections on a socket
    server.</p>
  </div>
</body>
</html>
