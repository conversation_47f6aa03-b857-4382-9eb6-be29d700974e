use moka::future::Cache;
use server_protoes::TaMessageRespPack;
use std::io;
use tokio::sync::mpsc;
use tracing::*;

// 客户端连接信息
#[derive(Debug, <PERSON><PERSON>)]
struct ClientConnection {
    sender: mpsc::Sender<TaMessageRespPack>, // 用于发送数据给客户端
    clientid: String,                        //客户ID，用于跟ip地址关联，并返回正确的数据
}

#[derive(Debug, Clone)]
pub struct ConnectionManager {
    clients: Cache<String, ClientConnection>,
}

impl ConnectionManager {
    pub fn new() -> Self {
        Self {
            clients: Cache::builder().build(),
        }
    }

    pub async fn add_client(&self, addr: String, sender: mpsc::Sender<TaMessageRespPack>) {
        let _ = self
            .clients
            .insert(
                addr,
                ClientConnection {
                    sender,
                    clientid: "".to_string(),
                },
            )
            .await;
    }

    //登录后，更新连接信息，把IP地址和客户ID关联起来
    pub async fn update_client(&self, addr: String, clientid: &str) {
        info!(addr, clientid, "调整连接客户端的clientid");
        if let Some(mut conn) = self.clients.get(&addr).await {
            conn.clientid = clientid.to_string();
            let _ = self.clients.insert(addr, conn).await;
        }
        info!("当前的连接信息 :{:?}", self.clients);
        // if conn.is_none(){
        // }
        // let _ = self.clients.insert(addr, ClientConnection { sender }).await;
    }

    pub async fn remove_client(&self, addr: &str) {
        let _ = self.clients.remove(addr).await;
    }

    pub async fn exists_client(&self, clientid: &str) -> bool {
        for (_key, client) in self.clients.iter() {
            info!("exists_client,{},{}", client.clientid, clientid);
            if client.clientid.eq_ignore_ascii_case(clientid) {
                return true;
            }
        }
        false
    }

    // 向特定客户端发送消息
    pub async fn send_to_client_id(&self, clientid: &str, response: TaMessageRespPack) -> Result<(), io::Error> {
        info!("收到通知信息，检查订单是否需要通知给客户。 {}=>{:?}", clientid, response);
        //let mut valid = false;
        for (_key, client) in self.clients.iter() {
            if client.clientid.eq_ignore_ascii_case(clientid) {
                client.sender.send(response).await.map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
                //valid = true;
                break;
            }
        }
        //其他客户的推送默认不处理
        // if !valid {
        //     return Err(io::Error::new(io::ErrorKind::NotFound, "Client not found"));
        // }

        Ok(())
    }

    // 向特定客户端发送消息
    pub async fn send_to_client(&self, addr: &str, response: TaMessageRespPack) -> Result<(), io::Error> {
        if let Some(client) = self.clients.get(addr).await {
            client.sender.send(response).await.map_err(|e| io::Error::new(io::ErrorKind::Other, e))?;
            Ok(())
        } else {
            Err(io::Error::new(io::ErrorKind::NotFound, "Client not found"))
        }
    }

    // 广播消息给所有客户端
    // async fn broadcast(&self, response: TaMessageRespPack) {
    //     for client in self.clients. {
    //         let _ = client.sender.send(response.clone()).await;
    //     }
    // }
}
