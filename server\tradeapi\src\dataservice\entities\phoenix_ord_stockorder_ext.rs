//! `SeaORM` Entity. Generated by sea-orm-codegen 0.12.4

use sea_orm::entity::prelude::*;

#[derive(
    Clone, Debug, <PERSON><PERSON>ult, PartialEq, DeriveEntityModel, Eq, serde::Serialize, serde::Deserialize,
)]
#[sea_orm(table_name = "phoenix_ord_stockorder_ext")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub order_id: i64,
    pub unit_id: i64,
    pub ext_order_id: String,
    pub create_date: i64,
    pub clientid: i64,
}

#[derive(Co<PERSON>, <PERSON>lone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
