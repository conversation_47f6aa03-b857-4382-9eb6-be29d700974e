use crate::QuickFixError;

/// Represent any day of the week.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>q, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h)]
#[repr(i32)]
#[allow(missing_docs)]
pub enum DayOfWeek {
    Sunday = 1,
    Monday = 2,
    Tuesday = 3,
    Wednesday = 4,
    Thursday = 5,
    Friday = 6,
    Saturday = 7,
}

impl TryFrom<i32> for DayOfWeek {
    type Error = QuickFixError;

    fn try_from(value: i32) -> Result<Self, Self::Error> {
        match value {
            1 => Ok(Self::Sunday),
            2 => Ok(Self::Monday),
            3 => Ok(Self::Tuesday),
            4 => Ok(Self::Wednesday),
            5 => Ok(Self::Thursday),
            6 => Ok(Self::Friday),
            7 => Ok(Self::Saturday),
            _ => Err(QuickFixError::InvalidArgument(format!(
                "Invalid day of week: {value}"
            ))),
        }
    }
}
