use config::{Config, ConfigError, File};
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct DbConfig {
    pub financedb: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct FixTag {
    pub channel: Vec<i64>,
    pub client_id: String,
    pub password: String,
    pub account: String,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct System {
    pub orderrouter: String,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
#[allow(dead_code)]
pub struct Settings {
    pub dbconfig: DbConfig,
    pub system: System,
    pub fixtag: FixTag,
}

impl Settings {
    pub fn init(cfgfile: &str) -> Result<Self, ConfigError> {
        let s = Config::builder().add_source(File::with_name(cfgfile)).build().expect("build config file error");

        s.try_deserialize()
    }
}
