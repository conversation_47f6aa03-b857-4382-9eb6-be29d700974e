<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>
  <link href="doxygen.css" rel="stylesheet" type="text/css">

  <title>Configuring QuickFIX</title>
</head>

<body>
  <div class='header'>
    <div class='headerTitle'>
      Configuring QuickFIX
    </div>
  </div>

  <div class='contents'>
    <p>A quickfix acceptor or initiator can maintain multiple FIX
    sessions. A FIX session is defined in QuickFIX as a unique
    combination of a <b>BeginString</b> (the FIX version number), a
    <b>SenderCompID</b> (your ID), and a <b>TargetCompID</b> (the
    ID of your counterparty). A <b>SessionQualifier</b> can also be
    use to disambiguate otherwise identical sessions.</p>

    <p>The SessionSettings class has the ability to pull settings
    out of any c++ stream such as a file stream. You can also pass
    it a filename. If you decide to write your own components,
    (storage for a particular database, a new kind of connector
    etc...), you can also use this pass in settings.</p>

    <p>A settings file is set up with two types of headings, a
    <b>[DEFAULT]</b> and a <b>[SESSION]</b> heading.
    <b>[SESSION]</b> tells QuickFIX that a new Session is being
    defined. <b>[DEFAULT]</b> is a where you can define settings
    that all sessions use by default. If you do not provide a
    setting that QuickFIX needs, it will throw a ConfigError
    telling you what setting is missing or improperly
    formatted.</p>

    <br/><hr/>
    <h1>Sample Settings File</h1>
    <pre class='fragment'>
# default settings for sessions
[DEFAULT]
ConnectionType=initiator
ReconnectInterval=60
SenderCompID=TW

# session definition
[SESSION]
# inherit ConnectionType, ReconnectInterval and SenderCompID from default
BeginString=FIX.4.1
TargetCompID=ARCA
StartTime=12:30:00
EndTime=23:30:00
HeartBtInt=20
SocketConnectPort=9823
SocketConnectHost=***************
DataDictionary=somewhere/FIX41.xml

[SESSION]
BeginString=FIX.4.0
TargetCompID=ISLD
StartTime=12:00:00
EndTime=23:00:00
HeartBtInt=30
SocketConnectPort=8323
SocketConnectHost=***********
DataDictionary=somewhere/FIX40.xml

[SESSION]
BeginString=FIX.4.2
TargetCompID=INCA
StartTime=12:30:00
EndTime=21:30:00
# overide default setting for RecconnectInterval
ReconnectInterval=30
HeartBtInt=30
SocketConnectPort=6523
SocketConnectHost=*******
# (optional) alternate connection ports and hosts to cycle through on failover
SocketConnectPort1=8392
SocketConnectHost1=*******
SocketConnectPort2=2932
SocketConnectHost2=***********
DataDictionary=somewhere/FIX42.xml
</pre>

    <div>
      <br/><hr/>
      <h1>QuickFIX Settings</h1>

      <table class='doxtable'>
        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>Session</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="left" valign="middle">
          <td><b>BeginString</b></td>

          <td>Version of FIX this session should use</td>

          <td>FIXT.1.1<br>
          FIX.4.4<br>
          FIX.4.3<br>
          FIX.4.2<br>
          FIX.4.1<br>
          FIX.4.0</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SenderCompID</b></td>

          <td>Your ID as associated with this FIX session</td>

          <td>case-sensitive alpha-numeric string</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>TargetCompID</b></td>

          <td>Counter parties ID as associated with this FIX
          session</td>

          <td>case-sensitive alpha-numeric string</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SessionQualifier</b></td>

          <td>Additional qualifier to disambiguate otherwise
          identical sessions</td>

          <td>case-sensitive alpha-numeric string</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>DefaultApplVerID</b></td>

          <td>Required only for FIXT 1.1 (and newer). Ignored for
          earlier transport versions. Specifies the default
          application version ID for the session. This can either
          be the ApplVerID enum (see the ApplVerID field) or the
          BeginString for the default version.</td>

          <td>FIX.5.0SP2<br>
          FIX.5.0SP1<br>
          FIX.5.0<br>
          FIX.4.4<br>
          FIX.4.3<br>
          FIX.4.2<br>
          FIX.4.1<br>
          FIX.4.0<br>
          9<br>
          8<br>
          7<br>
          6<br>
          5<br>
          4<br>
          3<br>
          2</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ConnectionType</b></td>

          <td>Defines if session will act as an acceptor or an
          initiator</td>

          <td>initiator<br>
          acceptor</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>StartTime</b></td>

          <td>Time of day that this FIX session becomes
          activated</td>

          <td>time in the format of HH:MM:SS, time is represented
          in UTC</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>EndTime</b></td>

          <td>Time of day that this FIX session becomes
          deactivated</td>

          <td>time in the format of HH:MM:SS, time is represented
          in UTC</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>StartDay</b></td>

          <td>For week long sessions, the starting day of week for
          the session. Use in combination with StartTime.</td>

          <td>Day of week in English using any abbreviation (i.e.
          mo, mon, mond, monda, monday are all valid)</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>EndDay</b></td>

          <td>For week long sessions, the ending day of week for
          the session. Use in combination with EndTime.</td>

          <td>Day of week in English using any abbreviation (i.e.
          mo, mon, mond, monda, monday are all valid)</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>LogonTime</b></td>

          <td>Time of day that this session logs on</td>

          <td>time in the format of HH:MM:SS, time is represented
          in UTC</td>

          <td>SessionTime value</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>LogoutTime</b></td>

          <td>Time of day that this session logs out</td>

          <td>time in the format of HH:MM:SS, time is represented
          in UTC</td>

          <td>EndTime value</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>LogonDay</b></td>

          <td>For week long sessions, the day of week the session logs on. 
          Use in combination with LogonTime.</td>

          <td>Day of week in English using any abbreviation (i.e.
          mo, mon, mond, monda, monday are all valid)</td>

          <td>StartDay value</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>LogoutDay</b></td>

          <td>For week long sessions, the day of week
          the session logs out. Use in combination with LogoutTime.</td>

          <td>Day of week in English using any abbreviation (i.e.
          mo, mon, mond, monda, monday are all valid)</td>

          <td>EndDay value</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>UseLocalTime</b></td>

          <td>Indicates StartTime and EndTime are expressed in
          localtime instead of UTC. Times in messages will still be
          set to UTC as this is required by the FIX
          specifications.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MillisecondsInTimeStamp</b></td>

          <td>Determines if milliseconds should be added to
          timestamps. Only available for FIX.4.2 and greater.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>
        
        <tr align="left" valign="middle">
          <td><b>TimestampPrecision</b></td>

          <td>Used to set the fractional part of timestamp. Allowable values are 0 to 9.
If set, overrrides MillisecondsInTimeStamp.</td>

          <td>0-9</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SendRedundantResendRequests</b></td>

          <td>If set to Y, QuickFIX will send all necessary resend
          requests, even if they appear redundant. Some systems
          will not certify the engine unless it does this. When set
          to N, QuickFIX will attempt to minimize resend requests.
          This is particularly useful on high volume systems.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ResetOnLogon</b></td>

          <td>Determines if sequence numbers should be reset when
          recieving a logon request. Acceptors only.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ResetOnLogout</b></td>

          <td>Determines if sequence numbers should be reset to 1
          after a normal logout termination.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ResetOnDisconnect</b></td>

          <td>Determines if sequence numbers should be reset to 1
          after an abnormal termination.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>RefreshOnLogon</b></td>

          <td>Determines if session state should be restored from
          persistence layer when logging on. Useful for creating
          hot failover sessions.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SendNextExpectedMsgSeqNum</b></td>

          <td>Adds tag NextExpectedMsgSeqNum (789) to the Logon
          message and enables session synchronisation at
          initiation.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>Validation</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="left" valign="middle">
          <td><b>UseDataDictionary</b></td>

          <td>Tell session whether or not to expect a data
          dictionary. You should always use a DataDictionary if you
          are using repeating groups.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>DataDictionary</b></td>

          <td>XML definition file for validating incoming FIX
          messages. If no DataDictionary is supplied, only basic
          message validation will be done<br>
          <br>
          This setting should only be used with FIX transport
          versions older than FIXT.1.1. See TransportDataDictionary
          and AppDataDictionary for FIXT.1.1 settings.</td>

          <td>valid XML data dictionary file, QuickFIX comes with
          the following defaults in the spec directory<br>
          <br>
          FIX44.xml<br>
          FIX43.xml<br>
          FIX42.xml<br>
          FIX41.xml<br>
          FIX40.xml</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>TransportDataDictionary</b></td>

          <td>XML definition file for validating admin (transport)
          messages. This setting is only valid for FIXT.1.1 (or
          newer) sessions.<br>
          <br>
          See DataDictionary for older transport versions (FIX.4.0
          - FIX.4.4) for additional information.</td>

          <td>valid XML data dictionary file, QuickFIX comes with
          the following defaults in the spec directory<br>
          <br>
          FIXT1.1.xml</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>AppDataDictionary</b></td>

          <td>
            XML definition file for validating application
            messages. This setting is only valid for FIXT.1.1 (or
            newer) sessions.<br>
            <br>
            See DataDictionary for older transport versions
            (FIX.4.0 - FIX.4.4) for additional information.<br>
            <br>
            This setting supports the possibility of a custom
            application data dictionary for each session. This
            setting would only be used with FIXT 1.1 and new
            transport protocols. This setting can be used as a
            prefix to specify multiple application dictionaries for
            the FIXT transport. For example:
            <pre class='fragment'>
DefaultApplVerID=FIX.4.2
# For default application version ID
AppDataDictionary=FIX42.xml
# For nondefault application version ID
# Use BeginString suffix for app version
AppDataDictionary.FIX.4.4=FIX44.xml
</pre>
          </td>

          <td>valid XML data dictionary file, QuickFIX comes with
          the following defaults in the spec directory<br>
          <br>
          FIX50SP2.xml<br>
          FIX50SP1.xml<br>
          FIX50.xml<br>
          FIX44.xml<br>
          FIX43.xml<br>
          FIX42.xml<br>
          FIX41.xml<br>
          FIX40.xml</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ValidateLengthAndChecksum</b></td>

          <td>If set to N, messages with incorrect length or
          checksum fields will not be rejected. You can also use
          this to force acceptance of repeating groups without a
          data dictionary. In this scenario you will not be able to
          access all repeating groups.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ValidateFieldsOutOfOrder</b></td>

          <td>If set to N, fields that are out of order (i.e. body
          fields in the header, or header fields in the body) will
          not be rejected. Useful for connecting to systems which
          do not properly order fields.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ValidateFieldsHaveValues</b></td>

          <td>If set to N, fields without values (empty) will not
          be rejected. Useful for connecting to systems which
          improperly send empty tags.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ValidateUserDefinedFields</b></td>

          <td>If set to N, user defined fields will not be rejected
          if they are not defined in the data dictionary, or are
          present in messages they do not belong to.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>
        
        <tr align="left" valign="middle">
          <td><b>PreserveMessageFieldsOrder</b></td>

          <td>Should the order of fields in the main outgoing message body be preserved or not (as defined in the configuration file). Default: only groups specified order is preserved.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>CheckCompID</b></td>

          <td>If set to Y, messages must be received from the
          counterparty with the correct SenderCompID and
          TargetCompID. Some systems will send you different
          CompIDs by design, so you must set this to N.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>CheckLatency</b></td>

          <td>If set to Y, messages must be received from the
          counterparty within a defined number of seconds (see
          MaxLatency). It is useful to turn this off if a system
          uses localtime for it's timestamps instead of GMT.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MaxLatency</b></td>

          <td>If CheckLatency is set to Y, this defines the number
          of seconds latency allowed for a message to be processed.
          Default is 120.</td>

          <td>positive integer</td>

          <td>120</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD">
          <h2>Miscellaneous</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="left" valign="middle">
          <td><b>HttpAcceptPort</b></td>

          <td>Port to listen to HTTP requests. Pointing a browser
          to this port will bring up a control panel. Must be in
          DEFAULT section.</td>

          <td>positive integer</td>

          <td></td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>Initiator</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ReconnectInterval</b></td>

          <td>Time between reconnection attempts in seconds. Only
          used for initiators</td>

          <td>positive integer</td>

          <td>30</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>HeartBtInt</b></td>

          <td>Heartbeat interval in seconds. Only used for
          initiators.</td>

          <td>positive integer</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>LogonTimeout</b></td>

          <td>Number of seconds to wait for a logon response before
          disconnecting.</td>

          <td>positive integer</td>

          <td>10</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>LogoutTimeout</b></td>

          <td>Number of seconds to wait for a logout response
          before disconnecting.</td>

          <td>positive integer</td>

          <td>2</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketConnectPort</b></td>

          <td>Socket port for connecting to a session. Only used
          with a SocketInitiator</td>

          <td>positive integer</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketConnectHost</b></td>

          <td>Host to connect to. Only used with a
          SocketInitiator</td>

          <td>valid IP address in the format of x.x.x.x or a domain
          name</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketConnectPort&lt;n&gt;</b></td>

          <td>Alternate socket ports for connecting to a session
          for failover, where <b>n</b> is a positive integer.
          (i.e.) SocketConnectPort1, SocketConnectPort2... must be
          consecutive and have a matching SocketConnectHost[n]</td>

          <td>positive integer</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketConnectHost&lt;n&gt;</b></td>

          <td>Alternate socket hosts for connecting to a session
          for failover, where <b>n</b> is a positive integer.
          (i.e.) SocketConnectHost1, SocketConnectHost2... must be
          consecutive and have a matching SocketConnectPort[n]</td>

          <td>valid IP address in the format of x.x.x.x or a domain
          name</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketNodelay</b></td>

          <td>Indicates a socket should be created with
          TCP_NODELAY. Currently, this must be defined in the
          [DEFAULT] section.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketSendBufferSize</b></td>

          <td>Indicates the size of SO_SNDBUF. Currently, this must be defined in the
          [DEFAULT] section.</td>

          <td>positive integer</td>

          <td>0</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketReceiveBufferSize</b></td>

          <td>Indicates the size of SO_RCVBUF. Currently, this must be defined in the
          [DEFAULT] section.</td>

          <td>positive integer</td>

          <td>0</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>Acceptor</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketAcceptPort</b></td>

          <td>Socket port for listening to incoming connections,
          Only used with a SocketAcceptor</td>

          <td>positive integer, valid open socket port. Currently,
          this must be defined in the [DEFAULT] section.</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketReuseAddress</b></td>

          <td>Indicates a socket should be created with
          SO_REUSADDR, Only used with a SocketAcceptor</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SocketNodelay</b></td>

          <td>Indicates a socket should be created with
          TCP_NODELAY. Currently, this must be defined in the
          [DEFAULT] section.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>Storage</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PersistMessages</b></td>

          <td>If set to N, no messages will be persisted. This will
          force QuickFIX to always send GapFills instead of
          resending messages. Use this if you know you never want
          to resend a message. Useful for market data streams.</td>

          <td>Y<br>
          N</td>

          <td>Y</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>FILE</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>FileStorePath</b></td>

          <td>Directory to store sequence number and message
          files.</td>

          <td>valid directory for storing files, must have write
          access</td>

          <td></td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>MYSQL</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLStoreDatabase</b></td>

          <td>Name of MySQL database to access for storing messages
          and session state.</td>

          <td>valid database for storing files, must have write
          access and correct DB shema</td>

          <td>quickfix</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLStoreUser</b></td>

          <td>User name logging in to MySQL database.</td>

          <td>valid user with read/write access to appropriate
          tables in database</td>

          <td>root</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLStorePassword</b></td>

          <td>Users password.</td>

          <td>correct MySQL password for user</td>

          <td>empty password</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLStoreHost</b></td>

          <td>Address of MySQL database.</td>

          <td>valid IP address in the format of x.x.x.x or a domain
          name</td>

          <td>localhost</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLStorePort</b></td>

          <td>Port of MySQL database.</td>

          <td>positive integer</td>

          <td>standard MySQL port</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLStoreUseConnectionPool</b></td>

          <td>Use database connection pools. When possible,
          sessions will share a single database connection.
          Otherwise each session gets its own connection.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>POSTGRESQL</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLStoreDatabase</b></td>

          <td>Name of PostgreSQL database to access for storing
          messages and session state.</td>

          <td>valid database for storing files, must have write
          access and correct DB shema</td>

          <td>quickfix</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLStoreUser</b></td>

          <td>User name logging in to PostgreSQL database.</td>

          <td>valid user with read/write access to appropriate
          tables in database</td>

          <td>postgres</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLStorePassword</b></td>

          <td>Users password.</td>

          <td>correct PostgreSQL password for user</td>

          <td>empty password</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLStoreHost</b></td>

          <td>Address of MySQL database.</td>

          <td>valid IP address in the format of x.x.x.x or a domain
          name</td>

          <td>localhost</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLStorePort</b></td>

          <td>Port of PostgreSQL database.</td>

          <td>positive integer</td>

          <td>standard PostgreSQL port</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLStoreUseConnectionPool</b></td>

          <td>Use database connection pools. When possible,
          sessions will share a single database connection.
          Otherwise each session gets its own connection.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>ODBC</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcStoreUser</b></td>

          <td>User name logging in to ODBC database.</td>

          <td>valid user with read/write access to appropriate
          tables in database. Ignored if UID is in the
          OdbcStoreConnectionString.</td>

          <td>sa</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcStorePassword</b></td>

          <td>Users password.</td>

          <td>correct ODBC password for user. Ignored if PWD is in
          the OdbcStoreConnectionString.</td>

          <td>empty password</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcStoreConnectionString</b></td>

          <td>ODBC connection string for database</td>

          <td>Valid ODBC connection string</td>

          <td>DATABASE=quickfix;DRIVER={SQL
          Server};SERVER=(local);</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>Logging</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>FILE</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>FileLogPath</b></td>

          <td>Directory to store logs.</td>

          <td>valid directory for storing files, must have write
          access</td>

          <td></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>FileLogBackupPath</b></td>

          <td>Directory to store backup logs.</td>

          <td>valid directory for storing backup files, must have
          write access</td>

          <td></td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>SCREEN</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ScreenLogShowIncoming</b></td>

          <td>Print incoming messages to standard out.</td>

          <td>Y<br>N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ScreenLogShowOutgoing</b></td>

          <td>Print outgoing messages to standard out.</td>

          <td>Y<br>N</td>

          <td>Y</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>ScreenLogShowEvents</b></td>

          <td>Print events to standard out.</td>

          <td>Y<br>N</td>

          <td>Y</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>MYSQL</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogDatabase</b></td>

          <td>Name of MySQL database to access for logging.</td>

          <td>valid database for storing files, must have write
          access and correct DB shema</td>

          <td>quickfix</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogUser</b></td>

          <td>User name logging in to MySQL database.</td>

          <td>valid user with read/write access to appropriate
          tables in database</td>

          <td>root</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogPassword</b></td>

          <td>Users password.</td>

          <td>correct MySQL password for user</td>

          <td>empty password</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogHost</b></td>

          <td>Address of MySQL database.</td>

          <td>valid IP address in the format of x.x.x.x or a domain
          name</td>

          <td>localhost</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogPort</b></td>

          <td>Port of MySQL database.</td>

          <td>positive integer</td>

          <td>standard MySQL port</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogUseConnectionPool</b></td>

          <td>Use database connection pools. When possible,
          sessions will share a single database connection.
          Otherwise each session gets its own connection.</td>

          <td>Y<br>N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogIncomingTable</b></td>

          <td>Name of table where incoming messages will be
          logged.</td>

          <td>Valid table with correct schema.</td>

          <td>messages_log</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogOutgoingTable</b></td>

          <td>Name of table where outgoing messages will be
          logged.</td>

          <td>Valid table with correct schema.</td>

          <td>messages_log</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>MySQLLogEventTable</b></td>

          <td>Name of table where events will be logged.</td>

          <td>Valid table with correct schema.</td>

          <td>event_log</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>POSTGRESQL</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLLogDatabase</b></td>

          <td>Name of PostgreSQL database to access for
          logging.</td>

          <td>valid database for storing files, must have write
          access and correct DB shema</td>

          <td>quickfix</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLLogUser</b></td>

          <td>User name logging in to PostgreSQL database.</td>

          <td>valid user with read/write access to appropriate
          tables in database</td>

          <td>postgres</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLLogPassword</b></td>

          <td>Users password.</td>

          <td>correct PostgreSQL password for user</td>

          <td>empty password</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLLogHost</b></td>

          <td>Address of PostgreSQL database.</td>

          <td>valid IP address in the format of x.x.x.x or a domain
          name</td>

          <td>localhost</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLLogPort</b></td>

          <td>Port of PostgreSQL database.</td>

          <td>positive integer</td>

          <td>standard PostgreSQL port</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgreSQLLogUseConnectionPool</b></td>

          <td>Use database connection pools. When possible,
          sessions will share a single database connection.
          Otherwise each session gets its own connection.</td>

          <td>Y<br>
          N</td>

          <td>N</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgresSQLLogIncomingTable</b></td>

          <td>Name of table where incoming messages will be
          logged.</td>

          <td>Valid table with correct schema.</td>

          <td>messages_log</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgresSQLLogOutgoingTable</b></td>

          <td>Name of table where outgoing messages will be
          logged.</td>

          <td>Valid table with correct schema.</td>

          <td>messages_log</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>PostgresSQLLogEventTable</b></td>

          <td>Name of table where events will be logged.</td>

          <td>Valid table with correct schema.</td>

          <td>event_log</td>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>ODBC</h3></td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcLogUser</b></td>

          <td>User name logging in to ODBC database.</td>

          <td>valid user with read/write access to appropriate
          tables in database</td>

          <td>sa</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcLogPassword</b></td>

          <td>Users password.</td>

          <td>correct ODBC password for user. Ignored if UID is in
          the OdbcLogConnectionString.</td>

          <td>empty password</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcLogConnectionString</b></td>

          <td>ODBC connection string for database</td>

          <td>Valid ODBC connection string. Ignored if PWD is in
          the OdbcStoreConnectionString.</td>

          <td>DATABASE=quickfix;DRIVER={SQL
          Server};SERVER=(local);</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcLogIncomingTable</b></td>

          <td>Name of table where incoming messages will be
          logged.</td>

          <td>Valid table with correct schema.</td>

          <td>messages_log</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcLogOutgoingTable</b></td>

          <td>Name of table where outgoing messages will be
          logged.</td>

          <td>Valid table with correct schema.</td>

          <td>messages_log</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>OdbcLogEventTable</b></td>

          <td>Name of table where events will be logged.</td>

          <td>Valid table with correct schema.</td>

          <td>event_log</td>
        </tr>
         <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#DDDDDD"><h2>SSL</h2></td>
        </tr>

        <tr align="left" valign="middle">
          <th>ID</th>

          <th>Description</th>

          <th>Valid Values</th>

          <th>Default</th>
        </tr>

        <tr align="center" valign="middle">
          <td colspan="4">Parameters have to be defined in the DEFAULT section.</td>
        </tr>

        <tr align="left" valign="middle">
          <td><b>SSLProtocol</b></td>

          <td>This directive can be used to control the SSL protocol flavors the application
          should use when establishing its environment.<br><br>

          The available (case-insensitive) protocols are:<br><br>

          SSLv2<br>

          This is the Secure Sockets Layer (SSL) protocol, version 2.0. It is the
          original SSL protocol as designed by Netscape Corporation.<br><br>

          SSLv3<br>

          This is the Secure Sockets Layer (SSL) protocol, version 3.0. It is the
          successor to SSLv2 and the currently (as of February 1999) de-facto
          standardized SSL protocol from Netscape Corporation. It&apos;s supported by
          almost all popular browsers.<br><br>

          TLSv1<br>

          This is the Transport Layer Security (TLS) protocol, version 1.0.<br><br>

          TLSv1_1<br>

          This is the Transport Layer Security (TLS) protocol, version 1.1.<br><br>

          TLSv1_2<br>

          This is the Transport Layer Security (TLS) protocol, version 1.2.<br><br>

          all<br>

          This is a shortcut for +SSLv2 +SSLv3 +TLSv1 +TLSv1_1 +TLSv1_2 and a convenient way for
          enabling all protocols except one when used in combination with the minus
          sign on a protocol as the example above shows.<br><br>

          Example:<br>

          enable all but not SSLv2<br>
          SSL_PROTOCOL = all -SSLv2</td>

          <td></td>

          <td>all -SSLv2</td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>SSLCipherSuite</b></td>

          <td>This complex directive uses a colon-separated cipher-spec string consisting
          of OpenSSL cipher specifications to configure the Cipher Suite the client is
          permitted to negotiate in the SSL handshake phase. Notice that this directive
          can be used both in per-server and per-directory context. In per-server
          context it applies to the standard SSL handshake when a connection is
          established. In per-directory context it forces a SSL renegotation with the
          reconfigured Cipher Suite after the HTTP request was read but before the HTTP
          response is sent.<br><br>

          An SSL cipher specification in cipher-spec is composed of 4 major attributes
          plus a few extra minor ones.<br><br>

          Key Exchange Algorithm:<br>
          RSA or Diffie-Hellman variants.<br><br>

          Authentication Algorithm:<br>
          RSA, Diffie-Hellman, DSS or none.<br><br>

          Cipher/Encryption Algorithm:<br>
          DES, Triple-DES, RC4, RC2, IDEA or none.<br><br>

          MAC Digest Algorithm:<br>
          MD5, SHA or SHA1.<br><br>

          For more details refer to mod_ssl documentation.<br><br>

          Example: RC4+RSA:+HIGH:</td>

          <td></td>

          <td>HIGH:!RC4</td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>CertificationAuthoritiesFile</b></td>

          <td>This directive sets the all-in-one file where you can assemble the
          Certificates of Certification Authorities (CA) whose clients you deal with.
          <td></td>

          <td></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>CertificationAuthoritiesDirectory</b></td>

          <td>This directive sets the directory where you keep the Certificates of
          Certification Authorities (CAs) whose clients you deal with.</td>

          <td></td>

          <td></td>
        </tr>
         <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>ACCEPTOR</h3></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>ServerCertificateFile</b></td>

          <td>This directive points to the PEM-encoded Certificate file and
          optionally also to the corresponding RSA or DSA Private Key file for it
          (contained in the same file).</td>

          <td></td>

          <td></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>ServerCertificateKeyFile</b></td>

          <td>This directive points to the PEM-encoded Private Key file. If
          the Private Key is not combined with the Certificate in the
          server certificate file, use this additional directive to point to the file with
          the stand-alone Private Key.</td>

          <td></td>

          <td></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>CertificateVerifyLevel</b></td>

          <td>This directive sets the Certificate verification level. 
          It applies to the authentication process used in the
          standard SSL handshake when a connection is established. 
          0 implies do not verify. 1 implies verify.</td>

          <td></td>

          <td></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>CertificateRevocationListFile</b></td>

          <td>This directive sets the all-in-one file where you can assemble the
          Certificate Revocation Lists (CRL) of Certification Authorities (CA) whose
          clients you deal with.</td>

          <td></td>

          <td></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>CertificateRevocationListDirectory</b></td>

          <td>This directive sets the directory where you keep the Certificate Revocation
          Lists (CRL) of Certification Authorities (CAs) whose clients you deal with.</td>

          <td></td>

          <td></td>
        </tr>
        
         <tr align="center" valign="middle">
          <td colspan="4" bgcolor="#BBBBBB"><h3>INITIATOR</h3></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>ClientCertificateFile</b></td>

          <td>This directive points to the PEM-encoded Certificate file and
          optionally also to the corresponding RSA or DSA Private Key file for it
          (contained in the same file).</td>

          <td></td>

          <td></td>
        </tr>
        <tr align="left" valign="middle">
          <td><b>ClientCertificateKeyFile</b></td>

          <td>This directive points to the PEM-encoded Private Key file. If
          the Private Key is not combined with the Certificate in the
          server certificate file, use this additional directive to point to the file with
          the stand-alone Private Key.</td>

          <td></td>

          <td></td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>
