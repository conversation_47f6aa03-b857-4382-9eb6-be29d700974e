[package]
name = "tradeapi"
version = "0.1.0"
edition = "2024"

[dependencies]
server-protoes = { workspace = true }
server-constant = { workspace = true }
utils = { workspace = true }
server-initialize = { workspace = true }

mqclient = { workspace = true }

config = { workspace = true }
serde = { workspace = true }

tracing = { workspace = true }
tokio = { workspace = true }
tokio-util = { workspace = true }

tonic = { workspace = true }
chrono = { workspace = true }
sea-orm = { workspace = true }

once_cell = { workspace = true }
moka = { workspace = true }
anyhow = { workspace = true }

futures-util = { workspace = true }
