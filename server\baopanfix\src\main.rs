mod config;
mod controller;
mod dataservice;
mod fixclient;
mod server;
mod service;

pub use config::*;
pub use controller::*;
pub use dataservice::*;
pub use fixclient::*;
pub use server::*;
pub use service::*;

use std::error::Error;
use tracing::*;

// #[tokio::main]
#[tokio::main(flavor = "multi_thread", worker_threads = 8)]
async fn main() -> Result<(), Box<dyn Error>> {
    let prefix = "fixclient";
    let dir = "./logs";
    let _guard = server_initialize::initialize_tracing(prefix, dir);

    let setting = Settings::init("config/fix.toml")?;
    info!("config: {:#?}", setting);

    ServerHandler::run(&setting).await;

    loop {
        tokio::time::sleep(std::time::Duration::from_secs(5)).await;
        continue;
    }
}
