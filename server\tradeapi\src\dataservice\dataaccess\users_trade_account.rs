use crate::dataservice::{
    dbsetup::DbConnection,
    entities::prelude::{UsersTradeAccount, UsersTradeAccountEntity},
    entities::users_trade_account,
};
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use tracing::*;
impl UsersTradeAccount {
    pub async fn query_account(db: &DbConnection, account: &String) -> Result<Option<UsersTradeAccount>, String> {
        let ret: Result<Option<UsersTradeAccount>, sea_orm::DbErr> = UsersTradeAccountEntity::find()
            .filter(users_trade_account::Column::AccountNo.eq(account))
            .one(db.get_connection())
            .await;
        if ret.as_ref().is_err() {
            error!("UsersTradeAccount query_account error,{:?}", ret);
            return Err("数据查询异常".to_string());
        }
        let ret = ret.unwrap();
        return Ok(ret);
    }
}
