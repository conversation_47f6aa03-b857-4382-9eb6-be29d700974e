use async_channel::unbounded;
use orderrouterclient::OrderRouterClientV2;
use server_protoes::RouterMsg;
use std::sync::Arc;
use tracing::{error, info};

use crate::dbsetup::DbConnection;
use crate::entities::prelude::SysTradeChannel;
use crate::{
    FixTradeRspHandler, GfFixTradeClient, Settings, TradeController,
    fixclient::{TFixTradeClient, YhFixTradeClient},
    init_trade_handler, order_from_router_to_fix, start_client,
};

pub struct ServerHandler;

impl ServerHandler {
    pub async fn run(setting: &Settings) {
        let db = DbConnection::new(&setting.dbconfig.financedb).await;

        let channel = SysTradeChannel::query_many(&setting.fixtag.channel, &db).await;
        info!("channel info: {:?}", &channel);
        // init_channel_info(channel).await;
        let channel_map = channel.iter().map(|c| (c.id, c.clone())).collect::<std::collections::HashMap<_, _>>();

        //路由中心 ------>
        let (tx_order_from_router, rx_order_from_router) = unbounded::<RouterMsg>();
        // ------> 路由中心
        let (tx_repay_to_router, rx_repay_to_router) = unbounded::<RouterMsg>();
        let (tx_resp, mut rx_resp) = tokio::sync::broadcast::channel::<RouterMsg>(100);

        let handler = FixTradeRspHandler {
            tx_repay_to_router: tx_resp.clone(),
        };
        init_trade_handler(&handler);

        let mut orderrouterclient = OrderRouterClientV2::new(
            &setting.system.orderrouter.to_string(),
            &setting.fixtag.channel,
            tx_order_from_router.clone(),
            rx_repay_to_router.clone(),
        )
        .await;
        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        let tx = tx_repay_to_router.clone();
        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = orderrouterclient.order_transfer(tx.clone()).await {
                            error!("{:?}", err);
                        }
                    }
                }
            }
        });

        // // FIX客户端 ------>
        // // tokio::spawn(async move {
        // //     if let Err(err) = FixTradeSer::init_fix(&*c) {
        // //         error!("Error starting FIX client: {}", err);
        // //     }
        // // });
        // // 用 spawn_blocking 包裹同步初始化
        // // FixTradeSer::init_fix(&*fix_client)?;
        // // thread::sleep(Duration::from_secs(10));
        // let tfix_client = Arc::new(TFixTradeClient::default());
        // let c = tfix_client.clone();
        // tokio::task::spawn_blocking(move || {
        //     let config_file = "./config/client.ini";
        //     if let Err(err) = FixTradeSer::init_fix_client(&*c, config_file) {
        //         error!("Error starting T FIX client: {}", err);
        //     }
        // });
        // let yhfix_client = Arc::new(YhFixTradeClient::default());
        // let c = yhfix_client.clone();
        // tokio::task::spawn_blocking(move || {
        //     let config_file = "./config/yhclient.ini";
        //     if let Err(err) = FixTradeSer::init_fix_client(&*c, config_file) {
        //         error!("Error starting YH FIX client: {}", err);
        //     }
        // });

        let t_fix_client = Arc::new(TFixTradeClient::new(
            &setting.fixtag.account,
            &setting.fixtag.password,
            &setting.fixtag.client_id,
            &channel_map,
        ));
        let yh_fix_client = Arc::new(YhFixTradeClient::new(
            &setting.fixtag.account,
            &setting.fixtag.password,
            &setting.fixtag.client_id,
            &channel_map,
        ));
        let gf_fix_client = Arc::new(GfFixTradeClient::new(
            &setting.fixtag.account,
            &setting.fixtag.password,
            &setting.fixtag.client_id,
            &channel_map,
        ));

        // let t = t_fix_client.clone();
        // let y = yh_fix_client.clone();
        // let g = gf_fix_client.clone();
        // let setting = setting.clone();
        if setting.fixtag.channel.iter().find(|&&x| x == 0).is_some() {
            info!("启动T FIX客户端");
            start_client(&t_fix_client, "./config/client.ini");
        } else if setting.fixtag.channel.iter().find(|&&x| x == 1).is_some() {
            info!("启动YH FIX客户端");
            start_client(&yh_fix_client, "./config/yhclient.ini");
        } else if setting.fixtag.channel.iter().find(|&&x| x == 38 || x == 39).is_some() {
            info!("启动GF FIX客户端");
            start_client(&gf_fix_client, "./config/gfclient.ini");
        }

        // info!("FIX client started 1: {:#?}", tfix_client);
        let trade_ctl = TradeController {
            tfix: t_fix_client,
            yhfix: yh_fix_client,
            gffix: gf_fix_client,
        };

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    result = rx_order_from_router.recv() => {
                        if let Ok(order) = result {
                            info!("收到订单：{:?}", order);
                            if let Some(order) = order_from_router_to_fix(&order) {
                                trade_ctl.send_fix(order);
                            }
                        }
                    }
                    result = rx_resp.recv() => {
                        if let Ok(resp) = result{
                            tx_repay_to_router.send(resp).await.unwrap();
                        }
                    }
                }
            }
        });
        // loop {}
    }
}
