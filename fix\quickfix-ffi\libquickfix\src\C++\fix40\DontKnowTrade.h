#ifndef FIX40_DONTKNOWTRADE_H
#define FIX40_DONTKNOWTRADE_H

#include "Message.h"

namespace FIX40
{

  class DontKnowTrade : public Message
  {
  public:
    DontKnowTrade() : Message(MsgType()) {}
    DontKnowTrade(const FIX::Message& m) : Message(m) {}
    DontKnowTrade(const Message& m) : Message(m) {}
    DontKnowTrade(const DontKnowTrade&) = default;
    DontKnowTrade(DontKnowTrade&&) = default;
    DontKnowTrade& operator=(const DontKnowTrade&) = default;
    DontKnowTrade& operator=(DontKnowTrade&&) = default;
    static FIX::MsgType MsgType() { return FIX::MsgType("Q"); }

    DontKnowTrade(
      const FIX::DKReason& aDKReason,
      const FIX::Symbol& aSymbol,
      const FIX::Side& aSide,
      const FIX::OrderQty& aOrderQty,
      const FIX::LastShares& aLastShares,
      const FIX::LastPx& aLastPx )
    : Message(MsgType())
    {
      set(aDKReason);
      set(aSymbol);
      set(aSide);
      set(aOrderQty);
      set(aLastShares);
      set(aLastPx);
    }

    FIELD_SET(*this, FIX::OrderID);
    FIELD_SET(*this, FIX::ExecID);
    FIELD_SET(*this, FIX::DKReason);
    FIELD_SET(*this, FIX::Symbol);
    FIELD_SET(*this, FIX::Side);
    FIELD_SET(*this, FIX::OrderQty);
    FIELD_SET(*this, FIX::LastShares);
    FIELD_SET(*this, FIX::LastPx);
    FIELD_SET(*this, FIX::Text);
  };

}

#endif
