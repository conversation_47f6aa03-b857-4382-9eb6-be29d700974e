[package]
name = "phoenix_fixbaopan"
authors.workspace = true
version.workspace = true
edition.workspace = true

[[bin]]
name = "phoenix_fixbaopan"
path = "src/main.rs"

# [[bin]]
# name = "phoenix_fixbaopan_gf"
# path = "src/phoenix_fixbaopan_gf.rs"

[[bin]]
name = "GfFixbaopan"
path = "src/main.rs"

[[bin]]
name = "YhFixbaopan"
path = "src/main.rs"

[dependencies]
server-initialize = { workspace = true }
server-protoes = { workspace = true }
orderrouterclient = { workspace = true }
logclient = { workspace = true }


quickfix = { workspace = true }
quickfix-ffi = { workspace = true }
quickfix-msg40 = { workspace = true }
quickfix-msg41 = { workspace = true }
quickfix-msg42 = { workspace = true }
quickfix-msg44 = { workspace = true }
quickfix-msg50 = { workspace = true }


anyhow = { workspace = true }
tracing = { workspace = true }
parking_lot = { workspace = true }
tokio = { workspace = true }
config = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
async-channel = { workspace = true }
once_cell = { workspace = true }
chrono = { workspace = true }
lazy_static = { workspace = true }
sea-orm = { workspace = true }
