use sea_orm::{ConnectOptions, Database, DatabaseConnection};
// use std::time::Duration;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct DbConnection {
    pub dbconn: DatabaseConnection,
}

impl DbConnection {
    pub async fn new(uri: &str) -> Self {
        let mut opt = ConnectOptions::new(uri.to_owned());

        opt.sqlx_logging(false);

        let db = Database::connect(opt).await.expect("can't connect to database");
        DbConnection { dbconn: db }
    }

    pub fn get_connection(&self) -> &DatabaseConnection {
        &self.dbconn
    }
}
