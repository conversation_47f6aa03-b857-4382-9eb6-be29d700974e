mod apiservice;
mod bpcomm;
mod svrconfig;

pub use apiservice::*;
pub use bpcomm::*;
pub use svrconfig::*;

use std::error::Error;
use tracing::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    let _worker_guard = server_initialize::initialize_tracing("phoenixbaopan", "./logs");

    let setting = Settings::new("config/baopan.toml").expect("init config error");
    info!("config: {:?}", setting);
    // let addr = format!("{}{}", setting.application.host, setting.application.port);

    // run_client(addr).await?;
    // Ok(())

    //connect to router server......

    let apiservice = ApiService::new(&setting).await;
    loop {
        match apiservice.run_service().await {
            Ok(_) => info!("退出"),
            Err(e) => {
                error!("connection error:{}", e);
                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
            }
        }
    }
}
