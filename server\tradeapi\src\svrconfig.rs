use config::{Config, ConfigError, File};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct ApplicationConfig {
    pub apphost: String,
    pub appport: i32,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct ServerConfig {
    pub ordercenter: String,
}
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct DbConfig {
    pub stockdb: String,
    pub financedb: String,
    pub customerdb: String,
}

#[derive(Debug, Default, Serialize, Clone, Deserialize)]
pub struct MqConfig {
    pub amqpaddr: String,
    pub vhost: String,
    pub notification_exchanger: String,
    pub accountriskcenter_routing_key: String,
}
#[derive(Debug, De<PERSON>ult, Serialize, Clone, Deserialize)]
pub struct LoginDataConfig {
    pub unit_id: String,
    pub pwd: String,
}

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, Serialize, Deserialize)]
pub struct Settings {
    pub application: ApplicationConfig,
    pub servers: ServerConfig,
    pub dbconfig: DbConfig,
    pub mq: MqConfig,
}

impl Settings {
    pub fn new(filename: &str) -> Result<Self, ConfigError> {
        let s = Config::builder()
            .add_source(File::with_name(filename))
            // .add_source(File::with_name("config/common.toml"))
            .build()
            .expect("build config file error");

        s.try_deserialize()
    }
}
